// types/next-auth.d.ts
// import Auth from "next-auth";

// export declare module "next-auth" {
//   interface Session {
//     user: {
//       id: string;
//       role: string;
//       name?: string | null;
//       email?: string | null;
//       image?: string | null;
//     };
//   }

//   interface User {
//     id: string;
//     role: string;
//   }
// }
// // types/next-auth.d.ts
// import "next-auth";

// declare module "next-auth" {
//   interface User {
//     role?: string;
//   }

//   interface Session {
//     user: {
//       id: string;
//       role?: string;
//     } & DefaultSession["user"];
//   }
// }

// types/next-auth.d.ts
import "next-auth";

declare module "next-auth" {
  interface User {
    role?: string;
    _id?: string;
  }

  interface Session {
    user: {
      id: string;
      role?: string;
    } & DefaultSession["user"];
  }
}