"use client";

import { useEffect, useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import AdminLayout from "@/components/admin/AdminLayout";
import { useParams, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "@/hooks/use-toast";
import { ALL_TOOLS } from "@/data/tools";

export default function EditToolPage() {
  const params = useParams();
  const router = useRouter();
  const toolId = params.id as string;

  const [tool, setTool] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    icon: "",
    category: "",
    popular: false,
    active: true,
    maxFileSize: 50,
    processingFee: 0,
    premiumOnly: false,
  });

  useEffect(() => {
    // In a real app, this would fetch from API
    const fetchTool = async () => {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500));

        const foundTool = ALL_TOOLS.find(t => t.id === toolId);
        if (foundTool) {
          const toolWithDefaults = {
            ...foundTool,
            active: true,
            maxFileSize: 50,
            processingFee: 0,
            premiumOnly: false,
            popular: foundTool.popular || false,
          };

          setTool(toolWithDefaults);
          setFormData({
            title: toolWithDefaults.title,
            description: toolWithDefaults.description,
            icon: toolWithDefaults.icon,
            category: toolWithDefaults.category,
            popular: toolWithDefaults.popular,
            active: toolWithDefaults.active,
            maxFileSize: toolWithDefaults.maxFileSize,
            processingFee: toolWithDefaults.processingFee,
            premiumOnly: toolWithDefaults.premiumOnly,
          });
        }
      } catch (error) {
        console.error("Error fetching tool:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchTool();
  }, [toolId]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast({
        title: "Success",
        description: "Tool settings updated successfully",
      });

      router.push("/admin/tools");
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update tool settings",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <AdminLayout title="Edit Tool">
        <div className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">Loading tool data...</p>
        </div>
      </AdminLayout>
    );
  }

  if (!tool) {
    return (
      <AdminLayout title="Edit Tool">
        <div className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">Tool not found</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title={`Edit Tool: ${tool.title}`}>
      <Card>
        <CardHeader>
          <CardTitle>Tool Settings</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="title">Tool Name</Label>
                <Input
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="icon">Icon</Label>
                <Input
                  id="icon"
                  name="icon"
                  value={formData.icon}
                  onChange={handleChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select
                  value={formData.category}
                  onValueChange={(value: string) => handleSelectChange("category", value)}
                >
                  <SelectTrigger id="category">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pdf">PDF</SelectItem>
                    <SelectItem value="office">Office</SelectItem>
                    <SelectItem value="image">Image</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="maxFileSize">Max File Size (MB)</Label>
                <Input
                  id="maxFileSize"
                  name="maxFileSize"
                  type="number"
                  min="1"
                  max="100"
                  value={formData.maxFileSize}
                  onChange={handleChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="processingFee">Processing Fee ($)</Label>
                <Input
                  id="processingFee"
                  name="processingFee"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.processingFee}
                  onChange={handleChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  required
                />
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="active">Active</Label>
                  <p className="text-sm text-muted-foreground">
                    Enable or disable this tool
                  </p>
                </div>
                <Switch
                  id="active"
                  checked={formData.active}
                  onCheckedChange={(checked: boolean) => handleSwitchChange("active", checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="popular">Popular</Label>
                  <p className="text-sm text-muted-foreground">
                    Mark this tool as popular
                  </p>
                </div>
                <Switch
                  id="popular"
                  checked={formData.popular}
                  onCheckedChange={(checked: boolean) => handleSwitchChange("popular", checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="premiumOnly">Premium Only</Label>
                  <p className="text-sm text-muted-foreground">
                    Restrict this tool to premium users only
                  </p>
                </div>
                <Switch
                  id="premiumOnly"
                  checked={formData.premiumOnly}
                  onCheckedChange={(checked: boolean) => handleSwitchChange("premiumOnly", checked)}
                />
              </div>
            </div>

            <div className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push("/admin/tools")}
              >
                Cancel
              </Button>
              <Button type="submit">Save Changes</Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </AdminLayout>
  );
}

