interface PerformanceMetric {
  url: string;
  method: string;
  duration: number;
  timestamp: number;
  status?: number;
  error?: string;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private readonly MAX_METRICS = 1000;
  private readonly SLOW_THRESHOLD = 1000;

  private marks: Record<string, number> = {};

  startTiming(url: string, method: string = 'GET'): (status?: number, error?: string) => void {
    const startTime = performance.now();

    return (status?: number, error?: string) => {
      const duration = performance.now() - startTime;

      this.recordMetric({
        url,
        method,
        duration,
        timestamp: Date.now(),
        status,
        error
      });
    };
  }

  private recordMetric(metric: PerformanceMetric) {
    this.metrics.push(metric);

    if (this.metrics.length > this.MAX_METRICS) {
      this.metrics = this.metrics.slice(-this.MAX_METRICS);
    }

    if (metric.duration > this.SLOW_THRESHOLD) {
      console.warn(`[PERF] Slow API request detected:`, {
        url: metric.url,
        method: metric.method,
        duration: `${metric.duration.toFixed(2)}ms`,
        status: metric.status
      });
    }
  }

  getStats(timeWindow?: number) {
    const now = Date.now();
    const windowStart = timeWindow ? now - timeWindow : 0;

    const relevantMetrics = this.metrics.filter(m => m.timestamp >= windowStart);

    if (relevantMetrics.length === 0) {
      return {
        totalRequests: 0,
        averageDuration: 0,
        slowRequests: 0,
        errorRequests: 0,
        slowestRequests: []
      };
    }

    const durations = relevantMetrics.map(m => m.duration);
    const slowRequests = relevantMetrics.filter(m => m.duration > this.SLOW_THRESHOLD);
    const errorRequests = relevantMetrics.filter(m => m.error || (m.status && m.status >= 400));

    return {
      totalRequests: relevantMetrics.length,
      averageDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
      slowRequests: slowRequests.length,
      errorRequests: errorRequests.length,
      slowestRequests: relevantMetrics
        .sort((a, b) => b.duration - a.duration)
        .slice(0, 10)
        .map(m => ({
          url: m.url,
          method: m.method,
          duration: Math.round(m.duration),
          status: m.status,
          timestamp: new Date(m.timestamp).toISOString()
        }))
    };
  }

  getUrlStats(urlPattern: string) {
    const relevantMetrics = this.metrics.filter(m => m.url.includes(urlPattern));

    if (relevantMetrics.length === 0) return null;

    const durations = relevantMetrics.map(m => m.duration);

    return {
      url: urlPattern,
      requestCount: relevantMetrics.length,
      averageDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
      minDuration: Math.min(...durations),
      maxDuration: Math.max(...durations),
      recentRequests: relevantMetrics.slice(-5).map(m => ({
        duration: Math.round(m.duration),
        status: m.status,
        timestamp: new Date(m.timestamp).toISOString()
      }))
    };
  }

  clear() {
    this.metrics = [];
    this.marks = {};
  }

  exportMetrics() {
    return this.metrics.map(m => ({
      ...m,
      timestamp: new Date(m.timestamp).toISOString(),
      duration: Math.round(m.duration)
    }));
  }

  // ✅ NEW: mark()
  mark(label: string) {
    this.marks[label] = performance.now();
  }

  // ✅ NEW: measure()
  measure(name: string, startMark: string, endMark: string = name): number | null {
    const start = this.marks[startMark];
    const end = this.marks[endMark] ?? performance.now();

    if (start === undefined) {
      console.warn(`[PERF] Start mark "${startMark}" not found`);
      return null;
    }

    const duration = end - start;

    this.recordMetric({
      url: name,
      method: 'CUSTOM',
      duration,
      timestamp: Date.now()
    });

    return duration;
  }
}

// Global instance
const performanceMonitor = new PerformanceMonitor();

export async function monitoredFetch(url: string, options?: RequestInit): Promise<Response> {
  const endTiming = performanceMonitor.startTiming(url, options?.method);

  try {
    const response = await fetch(url, options);
    endTiming(response.status);
    return response;
  } catch (error) {
    endTiming(undefined, error instanceof Error ? error.message : 'Unknown error');
    throw error;
  }
}

export function getPerformanceStats(timeWindow?: number) {
  return performanceMonitor.getStats(timeWindow);
}

export function getUrlPerformanceStats(urlPattern: string) {
  return performanceMonitor.getUrlStats(urlPattern);
}

export function clearPerformanceMetrics() {
  performanceMonitor.clear();
}

export function exportPerformanceData() {
  return performanceMonitor.exportMetrics();
}

// ✅ expose new methods in hook
export function usePerformanceMonitor() {
  return {
    fetch: monitoredFetch,
    getStats: getPerformanceStats,
    getUrlStats: getUrlPerformanceStats,
    clear: clearPerformanceMetrics,
    export: exportPerformanceData,
    mark: (label: string) => performanceMonitor.mark(label),
    measure: (name: string, start: string, end?: string) =>
      performanceMonitor.measure(name, start, end)
  };
}

export default performanceMonitor;
