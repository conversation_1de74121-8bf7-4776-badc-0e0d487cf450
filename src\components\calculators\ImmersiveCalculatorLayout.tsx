"use client";

import { ReactNode, useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useRouter } from "next/navigation";
import { useTheme } from "@/hooks/useTheme";
import { Calculator } from "lucide-react"; // Import only specific icons needed
import * as LucideIcons from "lucide-react"; // Keep for dynamic icon rendering
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { FiArrowLeft, FiX, FiShare2, FiCopy } from "react-icons/fi";

interface ImmersiveCalculatorLayoutProps {
  children: ReactNode;
  title: string;
  description: string;
  iconName: string;
  category?: string;
  popular?: boolean;
}

export default function ImmersiveCalculatorLayout({
  children,
  title,
  description,
  iconName,
  category,
  popular = false,
}: ImmersiveCalculatorLayoutProps) {
  const { theme } = useTheme();
  const router = useRouter();
  const [isVisible, setIsVisible] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);

  const isDark = theme === 'dark';

  useEffect(() => {
    // Trigger entrance animation
    setIsVisible(true);
    
    // Show tooltip after a delay
    const tooltipTimer = setTimeout(() => {
      setShowTooltip(true);
    }, 2000);

    return () => clearTimeout(tooltipTimer);
  }, []);

  // Function to render the appropriate icon
  const renderIcon = (iconName: string, className: string = "w-8 h-8") => {
    const pascalCaseName = iconName
      .split("-")
      .map(part => part.charAt(0).toUpperCase() + part.slice(1))
      .join("");

    const IconComponent = (LucideIcons as any)[pascalCaseName];

    if (IconComponent) {
      return <IconComponent className={className} />;
    }

    return <LucideIcons.Calculator className={className} />;
  };

  // Handle back navigation
  const handleBack = () => {
    setIsVisible(false);
    setTimeout(() => {
      router.back();
    }, 300);
  };

  // Handle share functionality
  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: title,
          text: description,
          url: window.location.href,
        });
      } catch (err) {
        console.log('Error sharing:', err);
      }
    } else {
      // Fallback to copy URL
      navigator.clipboard.writeText(window.location.href);
      // You could show a toast here
    }
  };

  // Get category color
  const getCategoryColor = () => {
    const categoryColors = {
      finance: isDark 
        ? 'bg-gradient-to-r from-green-900/40 to-emerald-900/40 text-green-300 border border-green-700/50' 
        : 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border border-green-200',
      math: isDark 
        ? 'bg-gradient-to-r from-blue-900/40 to-indigo-900/40 text-blue-300 border border-blue-700/50' 
        : 'bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border border-blue-200',
      conversion: isDark 
        ? 'bg-gradient-to-r from-purple-900/40 to-pink-900/40 text-purple-300 border border-purple-700/50' 
        : 'bg-gradient-to-r from-purple-100 to-pink-100 text-purple-800 border border-purple-200',
      health: isDark 
        ? 'bg-gradient-to-r from-red-900/40 to-rose-900/40 text-red-300 border border-red-700/50' 
        : 'bg-gradient-to-r from-red-100 to-rose-100 text-red-800 border border-red-200',
      developer: isDark 
        ? 'bg-gradient-to-r from-cyan-900/40 to-teal-900/40 text-cyan-300 border border-cyan-700/50' 
        : 'bg-gradient-to-r from-cyan-100 to-teal-100 text-cyan-800 border border-cyan-200',
      education: isDark 
        ? 'bg-gradient-to-r from-amber-900/40 to-yellow-900/40 text-amber-300 border border-amber-700/50' 
        : 'bg-gradient-to-r from-amber-100 to-yellow-100 text-amber-800 border border-amber-200',
      lifestyle: isDark 
        ? 'bg-gradient-to-r from-orange-900/40 to-red-900/40 text-orange-300 border border-orange-700/50' 
        : 'bg-gradient-to-r from-orange-100 to-red-100 text-orange-800 border border-orange-200',
      business: isDark 
        ? 'bg-gradient-to-r from-slate-900/40 to-gray-900/40 text-slate-300 border border-slate-700/50' 
        : 'bg-gradient-to-r from-slate-100 to-gray-100 text-slate-800 border border-slate-200',
    };
    return categoryColors[category as keyof typeof categoryColors] || (isDark 
      ? 'bg-gray-800/50 text-gray-300 border border-gray-700/50' 
      : 'bg-gray-100 text-gray-800 border border-gray-200');
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Enhanced Immersive Background */}
      <div className="fixed inset-0 -z-10">
        {isDark ? (
          <div className="absolute inset-0">
            {/* Dark mode: Transparent black with linear texture */}
            <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900/95 to-gray-800/90" />
            
            {/* Texture Pattern */}
            <div className="absolute inset-0 opacity-20" style={{
              backgroundImage: `radial-gradient(circle at 1px 1px, rgba(255,255,255,0.1) 1px, transparent 0)`,
              backgroundSize: '20px 20px'
            }} />
            
            {/* Subtle gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-yellow-900/10 via-transparent to-orange-900/10" />
            
            {/* Animated background elements */}
            <motion.div
              className="absolute top-20 left-20 w-96 h-96 rounded-full bg-yellow-500/5 blur-3xl"
              animate={{
                x: [0, 50, 0],
                y: [0, 30, 0],
                opacity: [0.3, 0.6, 0.3]
              }}
              transition={{
                repeat: Infinity,
                duration: 12,
                ease: "easeInOut"
              }}
            />
            <motion.div
              className="absolute bottom-20 right-20 w-80 h-80 rounded-full bg-orange-500/5 blur-3xl"
              animate={{
                x: [0, -40, 0],
                y: [0, -25, 0],
                opacity: [0.3, 0.6, 0.3]
              }}
              transition={{
                repeat: Infinity,
                duration: 15,
                ease: "easeInOut"
              }}
            />
          </div>
        ) : (
          <div className="absolute inset-0">
            {/* Light mode: Solid light background */}
            <div className="absolute inset-0 bg-gradient-to-br from-yellow-50 via-white to-orange-50" />
            
            {/* Subtle texture */}
            <div className="absolute inset-0 opacity-30" style={{
              backgroundImage: `radial-gradient(circle at 1px 1px, rgba(251,191,36,0.1) 1px, transparent 0)`,
              backgroundSize: '24px 24px'
            }} />
          </div>
        )}
      </div>

      {/* Top Navigation Bar */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : -20 }}
        transition={{ duration: 0.5 }}
        className="fixed top-0 left-0 right-0 z-50 p-4"
      >
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
              className={`${
                isDark 
                  ? 'bg-black/20 hover:bg-black/30 text-white border border-white/10' 
                  : 'bg-white/80 hover:bg-white text-gray-900 border border-gray-200'
              } backdrop-blur-sm rounded-full px-4 py-2 transition-all duration-300`}
            >
              <FiArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
          </motion.div>

          <div className="flex items-center gap-3">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                variant="ghost"
                size="sm"
                onClick={handleShare}
                className={`${
                  isDark 
                    ? 'bg-black/20 hover:bg-black/30 text-white border border-white/10' 
                    : 'bg-white/80 hover:bg-white text-gray-900 border border-gray-200'
                } backdrop-blur-sm rounded-full p-2 transition-all duration-300`}
              >
                <FiShare2 className="w-4 h-4" />
              </Button>
            </motion.div>
          </div>
        </div>
      </motion.div>

      {/* Main Calculator Badge/Panel */}
      <div className="min-h-screen flex items-center justify-center p-4 pt-20">
        <motion.div
          initial={{ opacity: 0, scale: 0.8, y: 50 }}
          animate={{ 
            opacity: isVisible ? 1 : 0, 
            scale: isVisible ? 1 : 0.8, 
            y: isVisible ? 0 : 50 
          }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="w-full max-w-4xl"
        >
          {/* Calculator Header Badge */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className={`mb-6 p-6 rounded-2xl border backdrop-blur-sm ${
              isDark
                ? 'bg-gradient-to-br from-gray-900/80 via-gray-800/80 to-yellow-900/20 border-yellow-500/20'
                : 'bg-gradient-to-br from-yellow-50 via-white to-orange-50 border-yellow-200'
            } shadow-xl`}
          >
            <div className="flex items-start gap-4">
              <motion.div
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ duration: 0.3 }}
                className={`p-4 rounded-xl ${
                  isDark ? 'bg-yellow-500/20' : 'bg-yellow-100'
                }`}
              >
                {renderIcon(iconName, "w-8 h-8 text-yellow-600 dark:text-yellow-400")}
              </motion.div>
              
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h1 className={`text-2xl md:text-3xl font-bold ${
                    isDark ? 'text-white' : 'text-gray-900'
                  }`}>
                    {title}
                  </h1>
                  
                  {popular && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ duration: 0.5, delay: 0.4 }}
                    >
                      <Badge className={`${
                        isDark 
                          ? 'bg-gradient-to-r from-yellow-900/50 to-amber-900/50 text-yellow-300 border border-yellow-700/50' 
                          : 'bg-gradient-to-r from-yellow-100 to-amber-100 text-yellow-800 border border-yellow-200'
                      } px-3 py-1`}>
                        ⭐ Popular
                      </Badge>
                    </motion.div>
                  )}
                </div>
                
                <p className={`text-lg mb-3 ${
                  isDark ? 'text-gray-300' : 'text-gray-600'
                }`}>
                  {description}
                </p>
                
                {category && (
                  <motion.div
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                  >
                    <Badge className={`${getCategoryColor()} px-3 py-1 text-sm`}>
                      {category}
                    </Badge>
                  </motion.div>
                )}
              </div>
            </div>
          </motion.div>

          {/* Calculator Content */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className={`rounded-2xl border backdrop-blur-sm overflow-hidden shadow-2xl ${
              isDark
                ? 'bg-gradient-to-br from-gray-900/90 via-gray-800/90 to-gray-900/90 border-gray-700/50'
                : 'bg-white/95 border-gray-200'
            }`}
          >
            {children}
          </motion.div>
        </motion.div>
      </div>

      {/* Usage Tooltip */}
      <AnimatePresence>
        {showTooltip && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            transition={{ duration: 0.3 }}
            className="fixed bottom-6 right-6 z-50"
          >
            <div className={`p-4 rounded-xl border backdrop-blur-sm max-w-sm ${
              isDark
                ? 'bg-black/80 border-gray-700 text-white'
                : 'bg-white/90 border-gray-200 text-gray-900'
            } shadow-lg`}>
              <div className="flex items-start gap-3">
                <div className="text-blue-500">
                  <LucideIcons.Info className="w-5 h-5" />
                </div>
                <div>
                  <h4 className="font-semibold mb-1">Quick Tip</h4>
                  <p className="text-sm opacity-80">
                    Results update automatically as you type. Use the back button to return to the calculator list.
                  </p>
                </div>
                <button
                  onClick={() => setShowTooltip(false)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <FiX className="w-4 h-4" />
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
