import { NextRequest } from 'next/server';

// Types for rate limiting
export interface RateLimitResult {
  success: boolean;
  limit: number;
  remaining: number;
  reset: number;
  retryAfter?: number;
}

export interface RateLimitConfig {
  requests: number;
  window: number; // in seconds
  identifier?: string;
}

// Rate limit configurations for different endpoints
export const RATE_LIMIT_CONFIGS = {
  // General API routes: 100 requests per 15 minutes per IP
  general: {
    requests: 100,
    window: 15 * 60, // 15 minutes
  },
  // Contact form: 5 submissions per hour per IP
  contact: {
    requests: 5,
    window: 60 * 60, // 1 hour
  },
  // Authentication endpoints: 10 attempts per 15 minutes per IP
  auth: {
    requests: 10,
    window: 15 * 60, // 15 minutes
  },
  // Admin API routes: 50 requests per 15 minutes per user
  admin: {
    requests: 50,
    window: 15 * 60, // 15 minutes
  },
} as const;

// In-memory store for development (fallback)
class MemoryStore {
  private store = new Map<string, { count: number; resetTime: number }>();

  async get(key: string): Promise<{ count: number; resetTime: number } | null> {
    const now = Date.now();
    const entry = this.store.get(key);
    
    if (!entry || now > entry.resetTime) {
      return null;
    }
    
    return entry;
  }

  async set(key: string, count: number, resetTime: number): Promise<void> {
    this.store.set(key, { count, resetTime });
  }

  async increment(key: string, resetTime: number): Promise<number> {
    const entry = await this.get(key);
    const newCount = entry ? entry.count + 1 : 1;
    await this.set(key, newCount, resetTime);
    return newCount;
  }

  // Cleanup expired entries periodically
  cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of Array.from(this.store.entries())) {
      if (now > entry.resetTime) {
        this.store.delete(key);
      }
    }
  }
}

// Global memory store instance
const memoryStore = new MemoryStore();

// Cleanup expired entries every 5 minutes
if (typeof window === 'undefined') {
  setInterval(() => {
    memoryStore.cleanup();
  }, 5 * 60 * 1000);
}

// Enhanced in-memory store with better performance and cleanup
class EnhancedMemoryStore {
  private store = new Map<string, { count: number; resetTime: number }>();
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // Clean up expired entries every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  private cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of Array.from(this.store.entries())) {
      if (entry.resetTime <= now) {
        this.store.delete(key);
      }
    }
  }

  async get(key: string): Promise<{ count: number; resetTime: number } | null> {
    const entry = this.store.get(key);
    if (!entry) return null;

    // Check if entry has expired
    if (entry.resetTime <= Date.now()) {
      this.store.delete(key);
      return null;
    }

    return entry;
  }

  async set(key: string, count: number, resetTime: number): Promise<void> {
    this.store.set(key, { count, resetTime });
  }

  async increment(key: string, resetTime: number): Promise<number> {
    const entry = await this.get(key);
    const newCount = entry ? entry.count + 1 : 1;
    await this.set(key, newCount, resetTime);
    return newCount;
  }

  // Graceful shutdown
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.store.clear();
  }
}

// Use enhanced memory store for all environments (no Redis dependency)
const store = new EnhancedMemoryStore();

// Get client identifier (IP address with fallback)
export function getClientIdentifier(request: NextRequest): string {
  // Try to get real IP from various headers
  const forwarded = request.headers.get('x-forwarded-for');
  const realIp = request.headers.get('x-real-ip');
  const cfConnectingIp = request.headers.get('cf-connecting-ip');
  
  // Use the first available IP
  const ip = forwarded?.split(',')[0]?.trim() ||
            realIp ||
            cfConnectingIp ||
            'unknown';
  
  return ip;
}

// Get user identifier for authenticated requests
export function getUserIdentifier(request: NextRequest): string | null {
  const userId = request.headers.get('x-user-id');
  return userId || null;
}

// Main rate limiting function
export async function rateLimit(
  request: NextRequest,
  config: RateLimitConfig
): Promise<RateLimitResult> {
  try {
    // Determine identifier (user ID for authenticated requests, IP for others)
    const identifier = config.identifier || 
                      getUserIdentifier(request) || 
                      getClientIdentifier(request);
    
    // Create unique key for this endpoint and identifier
    const key = `rate_limit:${identifier}:${request.nextUrl.pathname}`;
    
    const now = Date.now();
    const windowStart = now - (config.window * 1000);
    const resetTime = now + (config.window * 1000);
    
    // Get current count
    const entry = await store.get(key);
    
    // If no entry or expired, start fresh
    if (!entry || now > entry.resetTime) {
      await store.set(key, 1, resetTime);
      return {
        success: true,
        limit: config.requests,
        remaining: config.requests - 1,
        reset: resetTime,
      };
    }
    
    // Check if limit exceeded
    if (entry.count >= config.requests) {
      const retryAfter = Math.ceil((entry.resetTime - now) / 1000);
      return {
        success: false,
        limit: config.requests,
        remaining: 0,
        reset: entry.resetTime,
        retryAfter,
      };
    }
    
    // Increment count
    const newCount = await store.increment(key, entry.resetTime);
    
    return {
      success: true,
      limit: config.requests,
      remaining: Math.max(0, config.requests - newCount),
      reset: entry.resetTime,
    };
  } catch (error) {
    console.error('Rate limiting error:', error);
    // On error, allow the request (fail open)
    return {
      success: true,
      limit: config.requests,
      remaining: config.requests,
      reset: Date.now() + (config.window * 1000),
    };
  }
}

// Helper function to determine rate limit config based on route
export function getRateLimitConfig(pathname: string): RateLimitConfig {
  if (pathname.startsWith('/api/contact')) {
    return RATE_LIMIT_CONFIGS.contact;
  }
  
  if (pathname.startsWith('/api/auth/')) {
    return RATE_LIMIT_CONFIGS.auth;
  }
  
  if (pathname.startsWith('/api/admin/')) {
    return RATE_LIMIT_CONFIGS.admin;
  }
  
  return RATE_LIMIT_CONFIGS.general;
}

// Create rate limit response
export function createRateLimitResponse(result: RateLimitResult): Response {
  const headers = new Headers({
    'Content-Type': 'application/json',
    'X-RateLimit-Limit': result.limit.toString(),
    'X-RateLimit-Remaining': result.remaining.toString(),
    'X-RateLimit-Reset': result.reset.toString(),
  });
  
  if (result.retryAfter) {
    headers.set('Retry-After', result.retryAfter.toString());
  }
  
  return new Response(
    JSON.stringify({
      error: 'Too many requests',
      message: `Rate limit exceeded. Try again in ${result.retryAfter} seconds.`,
      retryAfter: result.retryAfter,
    }),
    {
      status: 429,
      headers,
    }
  );
}
