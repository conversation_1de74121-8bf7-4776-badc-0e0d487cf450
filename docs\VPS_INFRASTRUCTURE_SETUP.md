# 🖥️ VPS Infrastructure Setup Guide

## 📋 Overview

Complete setup guide for deploying ToolRapter on Hostinger VPS with enterprise-grade security and performance.

**Target Environment:**
- **VPS Provider:** Hostinger Ubuntu 22.04 LTS
- **IP Address:** ************
- **Domain:** toolrapter.com
- **SSH User:** root

## 🔧 Prerequisites

- SSH access to VPS
- Domain configured with DNS pointing to VPS IP
- GitHub repository access
- Basic Linux command knowledge

## 🚀 Step 1: Initial Server Setup

### 1.1 Connect to VPS
```bash
ssh root@************
```

### 1.2 Update System
```bash
# Update package lists
apt update && apt upgrade -y

# Install essential packages
apt install -y curl wget git unzip software-properties-common apt-transport-https ca-certificates gnupg lsb-release
```

### 1.3 Create Application User
```bash
# Create dedicated user for application
useradd -m -s /bin/bash toolrapter
usermod -aG sudo toolrapter

# Set up SSH for application user
mkdir -p /home/<USER>/.ssh
cp ~/.ssh/authorized_keys /home/<USER>/.ssh/
chown -R toolrapter:toolrapter /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh
chmod 600 /home/<USER>/.ssh/authorized_keys
```

## 🟢 Step 2: Node.js Installation

### 2.1 Install Node.js 20 LTS
```bash
# Add NodeSource repository
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -

# Install Node.js
apt install -y nodejs

# Verify installation
node --version  # Should show v20.x.x
npm --version   # Should show 10.x.x
```

### 2.2 Configure npm for Production
```bash
# Set npm to use production optimizations
npm config set fund false
npm config set audit false
npm config set progress false
```

## 🗄️ Step 3: MongoDB Setup

### 3.1 Install MongoDB Community Edition
```bash
# Import MongoDB public GPG key
wget -qO - https://www.mongodb.org/static/pgp/server-7.0.asc | sudo apt-key add -

# Add MongoDB repository
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu jammy/mongodb-org/7.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-7.0.list

# Update package list and install
apt update
apt install -y mongodb-org

# Start and enable MongoDB
systemctl start mongod
systemctl enable mongod

# Verify installation
systemctl status mongod
```

### 3.2 Secure MongoDB
```bash
# Connect to MongoDB shell
mongosh

# Create admin user
use admin
db.createUser({
  user: "admin",
  pwd: "STRONG_PASSWORD_HERE",
  roles: ["userAdminAnyDatabase", "dbAdminAnyDatabase", "readWriteAnyDatabase"]
})

# Create application database and user
use toolrapter
db.createUser({
  user: "toolrapter_user",
  pwd: "STRONG_APP_PASSWORD_HERE",
  roles: ["readWrite"]
})

exit
```

### 3.3 Configure MongoDB Security
```bash
# Edit MongoDB configuration
nano /etc/mongod.conf

# Add these configurations:
# security:
#   authorization: enabled
# net:
#   bindIp: 127.0.0.1
#   port: 27017

# Restart MongoDB
systemctl restart mongod
```

## ⚡ Step 4: PM2 Process Manager

### 4.1 Install PM2 Globally
```bash
npm install -g pm2

# Configure PM2 startup
pm2 startup
# Follow the instructions provided by the command

# Save PM2 configuration
pm2 save
```

### 4.2 Configure PM2 for Application
```bash
# Create application directory
mkdir -p /var/www/toolrapter
chown -R toolrapter:toolrapter /var/www/toolrapter

# Create PM2 ecosystem file (will be provided by deployment)
```

## 🌐 Step 5: Nginx Reverse Proxy

### 5.1 Install Nginx
```bash
apt install -y nginx

# Start and enable Nginx
systemctl start nginx
systemctl enable nginx

# Check status
systemctl status nginx
```

### 5.2 Configure Nginx for ToolRapter
```bash
# Remove default configuration
rm /etc/nginx/sites-enabled/default

# Create ToolRapter configuration
nano /etc/nginx/sites-available/toolrapter.com
```

Add the following configuration:
```nginx
server {
    listen 80;
    server_name toolrapter.com www.toolrapter.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name toolrapter.com www.toolrapter.com;
    
    # SSL Configuration (will be configured with Certbot)
    ssl_certificate /etc/letsencrypt/live/toolrapter.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/toolrapter.com/privkey.pem;
    
    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=general:10m rate=100r/s;
    
    # Main application
    location / {
        limit_req zone=general burst=20 nodelay;
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    # API rate limiting
    location /api/ {
        limit_req zone=api burst=5 nodelay;
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Static files caching
    location /_next/static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        proxy_pass http://localhost:3000;
    }
    
    # Images caching
    location /images/ {
        expires 30d;
        add_header Cache-Control "public";
        proxy_pass http://localhost:3000;
    }
}
```

### 5.3 Enable Nginx Configuration
```bash
# Enable the site
ln -s /etc/nginx/sites-available/toolrapter.com /etc/nginx/sites-enabled/

# Test configuration
nginx -t

# Reload Nginx
systemctl reload nginx
```

## 🔒 Step 6: SSL Certificate with Certbot

### 6.1 Install Certbot
```bash
# Install snapd
apt install -y snapd

# Install certbot via snap
snap install --classic certbot

# Create symlink
ln -s /snap/bin/certbot /usr/bin/certbot
```

### 6.2 Obtain SSL Certificate
```bash
# Stop Nginx temporarily
systemctl stop nginx

# Obtain certificate
certbot certonly --standalone -d toolrapter.com -d www.toolrapter.com

# Start Nginx
systemctl start nginx

# Test automatic renewal
certbot renew --dry-run
```

### 6.3 Configure Auto-renewal
```bash
# Add cron job for auto-renewal
crontab -e

# Add this line:
# 0 12 * * * /usr/bin/certbot renew --quiet && systemctl reload nginx
```

## 🔥 Step 7: Firewall Configuration

### 7.1 Configure UFW
```bash
# Reset UFW to defaults
ufw --force reset

# Set default policies
ufw default deny incoming
ufw default allow outgoing

# Allow SSH (change port if using custom)
ufw allow 22/tcp

# Allow HTTP and HTTPS
ufw allow 80/tcp
ufw allow 443/tcp

# Enable firewall
ufw enable

# Check status
ufw status verbose
```

## 🛡️ Step 8: Security Hardening

### 8.1 Install and Configure Fail2ban
```bash
# Install Fail2ban
apt install -y fail2ban

# Create custom configuration
cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local

# Edit configuration
nano /etc/fail2ban/jail.local
```

Add these configurations:
```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
maxretry = 3

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
port = http,https
logpath = /var/log/nginx/error.log

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
port = http,https
logpath = /var/log/nginx/error.log
maxretry = 10
```

### 8.2 Start Fail2ban
```bash
systemctl start fail2ban
systemctl enable fail2ban
systemctl status fail2ban
```

## 📊 Step 9: Monitoring Setup

### 9.1 Install System Monitoring Tools
```bash
# Install htop and iotop
apt install -y htop iotop

# Install log monitoring
apt install -y logwatch
```

### 9.2 Configure Log Rotation
```bash
# Create logrotate configuration for application
nano /etc/logrotate.d/toolrapter

# Add configuration:
# /var/www/toolrapter/logs/*.log {
#     daily
#     missingok
#     rotate 52
#     compress
#     delaycompress
#     notifempty
#     create 644 toolrapter toolrapter
# }
```

## 🔧 Step 10: Environment Configuration

### 10.1 Create Environment File
```bash
# Create environment file
nano /var/www/toolrapter/.env.production

# Add production environment variables (see .env.example)
```

### 10.2 Set Proper Permissions
```bash
# Set ownership
chown -R toolrapter:toolrapter /var/www/toolrapter

# Set permissions
chmod 755 /var/www/toolrapter
chmod 600 /var/www/toolrapter/.env.production
```

## ✅ Step 11: Verification

### 11.1 Test All Services
```bash
# Check all services
systemctl status nginx
systemctl status mongod
systemctl status fail2ban

# Check PM2 (after deployment)
pm2 status

# Check firewall
ufw status

# Test SSL
curl -I https://toolrapter.com
```

### 11.2 Performance Testing
```bash
# Test response time
curl -o /dev/null -s -w '%{time_total}\n' https://toolrapter.com

# Check memory usage
free -h

# Check disk usage
df -h
```

## 🚀 Next Steps

1. **Deploy Application**: Use GitHub Actions workflow to deploy
2. **Configure Monitoring**: Set up application monitoring
3. **Backup Strategy**: Implement automated backups
4. **Performance Optimization**: Fine-tune based on usage
5. **Security Auditing**: Regular security assessments

## 📞 Support

For issues or questions:
- Check logs: `/var/log/nginx/`, `/var/log/mongodb/`
- Monitor with: `pm2 logs`, `journalctl -u nginx`
- System monitoring: `htop`, `iotop`

---

**⚠️ Security Note:** Always use strong passwords, keep systems updated, and regularly review security configurations.
