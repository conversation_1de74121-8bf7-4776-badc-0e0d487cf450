// =============================================================================
// PERFORMANCE MONITORING UTILITIES
// =============================================================================
// Enterprise-grade performance monitoring for ToolRapter

interface PerformanceMetrics {
  responseTime: number;
  memoryUsage: NodeJS.MemoryUsage;
  cpuUsage: NodeJS.CpuUsage;
  timestamp: number;
  route?: string;
  method?: string;
  statusCode?: number;
}

interface PerformanceBenchmarks {
  maxResponseTime: number;
  maxMemoryUsage: number;
  maxCpuUsage: number;
  alertThresholds: {
    responseTime: number;
    memoryUsage: number;
    errorRate: number;
  };
}

// Enterprise performance benchmarks
export const PERFORMANCE_BENCHMARKS: PerformanceBenchmarks = {
  maxResponseTime: 5000, // 5 seconds max
  maxMemoryUsage: 1024 * 1024 * 1024, // 1GB max
  maxCpuUsage: 80, // 80% CPU max
  alertThresholds: {
    responseTime: 3000, // Alert if > 3 seconds
    memoryUsage: 512 * 1024 * 1024, // Alert if > 512MB
    errorRate: 5, // Alert if > 5% error rate
  },
};

// Performance metrics storage (in production, use Redis or database)
const metricsStore: PerformanceMetrics[] = [];
const MAX_METRICS_HISTORY = 1000;

// Performance timer class
export class PerformanceTimer {
  private startTime: number;
  private startCpuUsage: NodeJS.CpuUsage;
  private route?: string;
  private method?: string;

  constructor(route?: string, method?: string) {
    this.startTime = Date.now();
    this.startCpuUsage = process.cpuUsage();
    this.route = route;
    this.method = method;
  }

  end(statusCode?: number): PerformanceMetrics {
    const endTime = Date.now();
    const endCpuUsage = process.cpuUsage(this.startCpuUsage);
    const responseTime = endTime - this.startTime;

    const metrics: PerformanceMetrics = {
      responseTime,
      memoryUsage: process.memoryUsage(),
      cpuUsage: endCpuUsage,
      timestamp: endTime,
      route: this.route,
      method: this.method,
      statusCode,
    };

    // Store metrics (limit history)
    metricsStore.push(metrics);
    if (metricsStore.length > MAX_METRICS_HISTORY) {
      metricsStore.shift();
    }

    // Check for performance issues
    this.checkPerformanceAlerts(metrics);

    return metrics;
  }

  private checkPerformanceAlerts(metrics: PerformanceMetrics): void {
    const alerts: string[] = [];

    // Response time alert
    if (metrics.responseTime > PERFORMANCE_BENCHMARKS.alertThresholds.responseTime) {
      alerts.push(`High response time: ${metrics.responseTime}ms (threshold: ${PERFORMANCE_BENCHMARKS.alertThresholds.responseTime}ms)`);
    }

    // Memory usage alert
    if (metrics.memoryUsage.heapUsed > PERFORMANCE_BENCHMARKS.alertThresholds.memoryUsage) {
      alerts.push(`High memory usage: ${Math.round(metrics.memoryUsage.heapUsed / 1024 / 1024)}MB (threshold: ${Math.round(PERFORMANCE_BENCHMARKS.alertThresholds.memoryUsage / 1024 / 1024)}MB)`);
    }

    // Log alerts
    if (alerts.length > 0) {
      console.warn('Performance Alert:', {
        route: metrics.route,
        method: metrics.method,
        alerts,
        timestamp: new Date(metrics.timestamp).toISOString(),
      });
    }
  }
}

// Get performance statistics
export function getPerformanceStats(): {
  averageResponseTime: number;
  maxResponseTime: number;
  minResponseTime: number;
  totalRequests: number;
  errorRate: number;
  memoryUsage: {
    current: number;
    average: number;
    max: number;
  };
  recentMetrics: PerformanceMetrics[];
} {
  if (metricsStore.length === 0) {
    return {
      averageResponseTime: 0,
      maxResponseTime: 0,
      minResponseTime: 0,
      totalRequests: 0,
      errorRate: 0,
      memoryUsage: {
        current: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        average: 0,
        max: 0,
      },
      recentMetrics: [],
    };
  }

  const responseTimes = metricsStore.map(m => m.responseTime);
  const memoryUsages = metricsStore.map(m => m.memoryUsage.heapUsed);
  const errors = metricsStore.filter(m => m.statusCode && m.statusCode >= 400);

  return {
    averageResponseTime: Math.round(responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length),
    maxResponseTime: Math.max(...responseTimes),
    minResponseTime: Math.min(...responseTimes),
    totalRequests: metricsStore.length,
    errorRate: Math.round((errors.length / metricsStore.length) * 100),
    memoryUsage: {
      current: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
      average: Math.round(memoryUsages.reduce((a, b) => a + b, 0) / memoryUsages.length / 1024 / 1024),
      max: Math.round(Math.max(...memoryUsages) / 1024 / 1024),
    },
    recentMetrics: metricsStore.slice(-10), // Last 10 requests
  };
}

// Clear performance metrics
export function clearPerformanceMetrics(): void {
  metricsStore.length = 0;
}

// Performance middleware wrapper
export function withPerformanceMonitoring<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  route?: string,
  method?: string
): (...args: T) => Promise<R> {
  return async (...args: T): Promise<R> => {
    const timer = new PerformanceTimer(route, method);
    
    try {
      const result = await fn(...args);
      timer.end(200); // Assume success
      return result;
    } catch (error) {
      timer.end(500); // Assume error
      throw error;
    }
  };
}

// Check if system meets performance benchmarks
export function checkPerformanceBenchmarks(): {
  passed: boolean;
  issues: string[];
  recommendations: string[];
} {
  const stats = getPerformanceStats();
  const issues: string[] = [];
  const recommendations: string[] = [];

  // Check response time
  if (stats.averageResponseTime > PERFORMANCE_BENCHMARKS.maxResponseTime) {
    issues.push(`Average response time (${stats.averageResponseTime}ms) exceeds benchmark (${PERFORMANCE_BENCHMARKS.maxResponseTime}ms)`);
    recommendations.push('Consider optimizing database queries and implementing caching');
  }

  // Check memory usage
  if (stats.memoryUsage.current > PERFORMANCE_BENCHMARKS.maxMemoryUsage / 1024 / 1024) {
    issues.push(`Current memory usage (${stats.memoryUsage.current}MB) exceeds benchmark (${PERFORMANCE_BENCHMARKS.maxMemoryUsage / 1024 / 1024}MB)`);
    recommendations.push('Consider implementing memory optimization and garbage collection tuning');
  }

  // Check error rate
  if (stats.errorRate > PERFORMANCE_BENCHMARKS.alertThresholds.errorRate) {
    issues.push(`Error rate (${stats.errorRate}%) exceeds threshold (${PERFORMANCE_BENCHMARKS.alertThresholds.errorRate}%)`);
    recommendations.push('Review error logs and implement better error handling');
  }

  return {
    passed: issues.length === 0,
    issues,
    recommendations,
  };
}

// Export performance metrics for monitoring systems
export function exportMetricsForMonitoring(): {
  timestamp: string;
  metrics: {
    responseTime: {
      avg: number;
      max: number;
      min: number;
      p95: number;
    };
    memory: {
      used: number;
      total: number;
      percentage: number;
    };
    requests: {
      total: number;
      errorRate: number;
      successRate: number;
    };
    uptime: number;
  };
} {
  const stats = getPerformanceStats();
  const responseTimes = metricsStore.map(m => m.responseTime).sort((a, b) => a - b);
  const p95Index = Math.floor(responseTimes.length * 0.95);
  const memUsage = process.memoryUsage();

  return {
    timestamp: new Date().toISOString(),
    metrics: {
      responseTime: {
        avg: stats.averageResponseTime,
        max: stats.maxResponseTime,
        min: stats.minResponseTime,
        p95: responseTimes[p95Index] || 0,
      },
      memory: {
        used: Math.round(memUsage.heapUsed / 1024 / 1024),
        total: Math.round(memUsage.heapTotal / 1024 / 1024),
        percentage: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100),
      },
      requests: {
        total: stats.totalRequests,
        errorRate: stats.errorRate,
        successRate: 100 - stats.errorRate,
      },
      uptime: Math.round(process.uptime()),
    },
  };
}
