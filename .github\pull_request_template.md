# Pull Request

## 📋 Description
Brief description of the changes in this PR.

## 🔗 Related Issues
- Closes #[issue_number]
- Related to #[issue_number]

## 🎯 Type of Change
- [ ] 🐛 Bug fix (non-breaking change which fixes an issue)
- [ ] ✨ New feature (non-breaking change which adds functionality)
- [ ] 💥 Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] 📚 Documentation update
- [ ] 🎨 Style/UI changes
- [ ] ⚡ Performance improvement
- [ ] 🔧 Refactoring
- [ ] 🧪 Test updates

## 🧪 Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] E2E tests pass
- [ ] Manual testing completed
- [ ] Cross-browser testing (if UI changes)
- [ ] Mobile testing (if applicable)

## 📱 Mobile Testing (if applicable)
- [ ] iOS Safari
- [ ] Android Chrome
- [ ] Touch interactions work correctly
- [ ] Responsive design maintained
- [ ] Performance acceptable on mobile

## 🔒 Security Checklist
- [ ] No sensitive data exposed
- [ ] Input validation implemented
- [ ] Authentication/authorization checked
- [ ] CSRF protection maintained
- [ ] Rate limiting considered
- [ ] SQL injection prevention

## ⚡ Performance Checklist
- [ ] No performance regressions
- [ ] Bundle size impact minimal
- [ ] Database queries optimized
- [ ] Caching strategy considered
- [ ] Core Web Vitals maintained

## 📸 Screenshots (if applicable)
### Before
[Add screenshots of the current state]

### After
[Add screenshots of the changes]

## 🔄 Migration/Deployment Notes
- [ ] Database migrations required
- [ ] Environment variables added/changed
- [ ] Configuration changes needed
- [ ] Deployment order considerations

## 📚 Documentation
- [ ] README updated
- [ ] API documentation updated
- [ ] Code comments added
- [ ] Changelog updated

## ✅ Checklist
- [ ] My code follows the project's style guidelines
- [ ] I have performed a self-review of my code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] Any dependent changes have been merged and published

## 🎯 Reviewer Focus Areas
Please pay special attention to:
- [ ] Security implications
- [ ] Performance impact
- [ ] Mobile compatibility
- [ ] Accessibility
- [ ] Error handling
- [ ] Edge cases

## 📝 Additional Notes
Any additional information that reviewers should know about this PR.
