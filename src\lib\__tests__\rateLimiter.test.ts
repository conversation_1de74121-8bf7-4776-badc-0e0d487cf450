import { NextRequest } from 'next/server';
import {
  rateLimit,
  getRateLimitConfig,
  getClientIdentifier,
  getUserIdentifier,
  createRateLimitResponse,
  RATE_LIMIT_CONFIGS,
} from '../rateLimiter';

// Mock NextRequest
const createMockRequest = (pathname: string, headers: Record<string, string> = {}) => {
  return {
    nextUrl: { pathname },
    headers: new Map(Object.entries(headers)),
    ip: '127.0.0.1',
  } as unknown as NextRequest;
};

describe('Rate Limiter', () => {
  describe('RATE_LIMIT_CONFIGS', () => {
    it('should have all required configurations', () => {
      expect(RATE_LIMIT_CONFIGS).toHaveProperty('general');
      expect(RATE_LIMIT_CONFIGS).toHaveProperty('contact');
      expect(RATE_LIMIT_CONFIGS).toHaveProperty('auth');
      expect(RATE_LIMIT_CONFIGS).toHaveProperty('admin');
    });

    it('should have proper rate limit values', () => {
      expect(RATE_LIMIT_CONFIGS.general.requests).toBe(100);
      expect(RATE_LIMIT_CONFIGS.general.window).toBe(15 * 60);
      
      expect(RATE_LIMIT_CONFIGS.contact.requests).toBe(5);
      expect(RATE_LIMIT_CONFIGS.contact.window).toBe(60 * 60);
      
      expect(RATE_LIMIT_CONFIGS.auth.requests).toBe(10);
      expect(RATE_LIMIT_CONFIGS.auth.window).toBe(15 * 60);
      
      expect(RATE_LIMIT_CONFIGS.admin.requests).toBe(50);
      expect(RATE_LIMIT_CONFIGS.admin.window).toBe(15 * 60);
    });
  });

  describe('getRateLimitConfig', () => {
    it('should return contact config for contact endpoints', () => {
      const config = getRateLimitConfig('/api/contact');
      expect(config).toEqual(RATE_LIMIT_CONFIGS.contact);
    });

    it('should return auth config for auth endpoints', () => {
      const config = getRateLimitConfig('/api/auth/signin');
      expect(config).toEqual(RATE_LIMIT_CONFIGS.auth);
    });

    it('should return admin config for admin endpoints', () => {
      const config = getRateLimitConfig('/api/admin/users');
      expect(config).toEqual(RATE_LIMIT_CONFIGS.admin);
    });

    it('should return general config for other endpoints', () => {
      const config = getRateLimitConfig('/api/blog/posts');
      expect(config).toEqual(RATE_LIMIT_CONFIGS.general);
    });
  });

  describe('getClientIdentifier', () => {
    it('should extract IP from x-forwarded-for header', () => {
      const request = createMockRequest('/api/test', {
        'x-forwarded-for': '***********, ********',
      });
      
      const identifier = getClientIdentifier(request);
      expect(identifier).toBe('***********');
    });

    it('should extract IP from x-real-ip header', () => {
      const request = createMockRequest('/api/test', {
        'x-real-ip': '***********',
      });
      
      const identifier = getClientIdentifier(request);
      expect(identifier).toBe('***********');
    });

    it('should extract IP from cf-connecting-ip header', () => {
      const request = createMockRequest('/api/test', {
        'cf-connecting-ip': '***********',
      });
      
      const identifier = getClientIdentifier(request);
      expect(identifier).toBe('***********');
    });

    it('should fallback to request.ip', () => {
      const request = createMockRequest('/api/test');
      
      const identifier = getClientIdentifier(request);
      expect(identifier).toBe('127.0.0.1');
    });

    it('should return unknown if no IP available', () => {
      const request = {
        nextUrl: { pathname: '/api/test' },
        headers: new Map(),
        ip: undefined,
      } as unknown as NextRequest;
      
      const identifier = getClientIdentifier(request);
      expect(identifier).toBe('unknown');
    });
  });

  describe('getUserIdentifier', () => {
    it('should extract user ID from x-user-id header', () => {
      const request = createMockRequest('/api/test', {
        'x-user-id': 'user123',
      });
      
      const identifier = getUserIdentifier(request);
      expect(identifier).toBe('user123');
    });

    it('should return null if no user ID header', () => {
      const request = createMockRequest('/api/test');
      
      const identifier = getUserIdentifier(request);
      expect(identifier).toBeNull();
    });
  });

  describe('createRateLimitResponse', () => {
    it('should create proper rate limit response', () => {
      const rateLimitResult = {
        success: false,
        limit: 100,
        remaining: 0,
        reset: Date.now() + 900000,
        retryAfter: 900,
      };

      const response = createRateLimitResponse(rateLimitResult);

      expect(response.status).toBe(429);
      expect(response.headers.get('X-RateLimit-Limit')).toBe('100');
      expect(response.headers.get('X-RateLimit-Remaining')).toBe('0');
      expect(response.headers.get('Retry-After')).toBe('900');
    });

    it('should include proper error message in response body', async () => {
      const rateLimitResult = {
        success: false,
        limit: 100,
        remaining: 0,
        reset: Date.now() + 900000,
        retryAfter: 900,
      };

      const response = createRateLimitResponse(rateLimitResult);
      const body = await response.json();

      expect(body).toHaveProperty('error', 'Rate limit exceeded');
      expect(body).toHaveProperty('message');
      expect(body).toHaveProperty('retryAfter', 900);
    });
  });

  describe('rateLimit', () => {
    beforeEach(() => {
      // Reset any mocks or state
      jest.clearAllMocks();
    });

    it('should allow requests within rate limit', async () => {
      const request = createMockRequest('/api/test');
      const config = RATE_LIMIT_CONFIGS.general;

      // Mock the store to return a low count
      const result = await rateLimit(request, config);

      expect(result.success).toBe(true);
      expect(result.limit).toBe(config.requests);
      expect(result.remaining).toBeGreaterThanOrEqual(0);
    });

    it('should handle rate limit configuration properly', async () => {
      const request = createMockRequest('/api/contact');
      const config = getRateLimitConfig('/api/contact');

      const result = await rateLimit(request, config);

      expect(result.limit).toBe(RATE_LIMIT_CONFIGS.contact.requests);
    });
  });
});
