#!/bin/bash

# =============================================================================
# TOOLRAPTER - QUICK DEPLOYMENT SCRIPT
# =============================================================================
# One-command deployment script for ToolCrush to toolrapter.com
# Usage: ./scripts/quick-deploy.sh

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🚀 ToolCrush Quick Deployment to toolrapter.com"
echo "================================================"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    log_error "Please run this script from the ToolCrush project root directory"
    exit 1
fi

# Check if required files exist
REQUIRED_FILES=(
    "ecosystem.config.js"
    "nginx.conf"
    ".github/workflows/deploy-vps.yml"
    "scripts/setup-vps.sh"
    "scripts/validate-deployment.sh"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        log_error "Required file missing: $file"
        exit 1
    fi
done

log_success "All required files present"

# Check Node.js version
NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    log_error "Node.js 18+ required. Current version: $(node --version)"
    exit 1
fi

log_success "Node.js version check passed"

# Install dependencies
log_info "Installing dependencies..."
if command -v pnpm &> /dev/null; then
    pnpm install
else
    npm install
fi

log_success "Dependencies installed"

# Run tests
log_info "Running tests..."
npm test

log_success "Tests passed"

# Build application
log_info "Building application..."
START_TIME=$(date +%s)
npm run build
END_TIME=$(date +%s)
BUILD_TIME=$((END_TIME - START_TIME))

if [ $BUILD_TIME -lt 20 ]; then
    log_success "Build completed in ${BUILD_TIME}s (< 20s target)"
else
    log_warning "Build took ${BUILD_TIME}s (> 20s target)"
fi

# Validate local deployment
log_info "Validating local build..."
npm start &
SERVER_PID=$!
sleep 5

# Test local server
if curl -s http://localhost:3000/api/health > /dev/null; then
    log_success "Local server validation passed"
else
    log_error "Local server validation failed"
    kill $SERVER_PID 2>/dev/null || true
    exit 1
fi

kill $SERVER_PID 2>/dev/null || true

# Check GitHub repository status
log_info "Checking GitHub repository status..."
if git remote -v | grep -q "MuhammadShahbaz195/ToolCrush"; then
    log_success "GitHub repository configured correctly"
else
    log_error "GitHub repository not configured correctly"
    exit 1
fi

# Check for uncommitted changes
if [ -n "$(git status --porcelain)" ]; then
    log_warning "You have uncommitted changes. Commit them before deployment."
    git status --short
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Check GitHub secrets
log_info "Checking GitHub secrets configuration..."
echo ""
echo "📋 Required GitHub Secrets Checklist:"
echo "Go to: https://github.com/MuhammadShahbaz195/ToolCrush/settings/secrets/actions"
echo ""
echo "Required secrets:"
echo "- VPS_HOST (toolrapter.com)"
echo "- VPS_USER (root)"
echo "- VPS_SSH_KEY (SSH private key)"
echo "- MONGODB_URI (MongoDB connection string)"
echo "- NEXTAUTH_SECRET (32-character secret)"
echo "- NEXTAUTH_URL (https://toolrapter.com)"
echo "- GOOGLE_CLIENT_ID (OAuth client ID)"
echo "- GOOGLE_CLIENT_SECRET (OAuth client secret)"
echo ""

read -p "Have you configured all GitHub secrets? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    log_error "Please configure GitHub secrets before deployment"
    echo "See docs/GITHUB_SECRETS_SETUP.md for detailed instructions"
    exit 1
fi

# VPS setup check
echo ""
log_info "VPS Infrastructure Setup"
echo "Run this command on your Hostinger VPS as root:"
echo ""
echo "curl -fsSL https://raw.githubusercontent.com/MuhammadShahbaz195/ToolCrush/main/scripts/setup-vps.sh | bash"
echo ""

read -p "Have you set up the VPS infrastructure? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    log_warning "Please set up VPS infrastructure first"
    echo "You can continue with deployment, but it may fail without proper VPS setup"
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Deploy to production
log_info "Deploying to production..."
echo ""
echo "🚀 Pushing to main branch to trigger deployment..."

# Add all changes and commit
if [ -n "$(git status --porcelain)" ]; then
    git add .
    git commit -m "feat: deploy to production - $(date '+%Y-%m-%d %H:%M:%S')"
fi

# Push to main branch
git push origin main

log_success "Deployment triggered!"

echo ""
echo "📊 Deployment Status:"
echo "- GitHub Actions: https://github.com/MuhammadShahbaz195/ToolCrush/actions"
echo "- Monitor deployment progress in GitHub Actions"
echo ""

# Wait for deployment
log_info "Waiting for deployment to complete..."
echo "This may take 5-10 minutes..."

# Monitor deployment (optional)
read -p "Monitor deployment progress? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "Opening GitHub Actions in browser..."
    if command -v xdg-open &> /dev/null; then
        xdg-open "https://github.com/MuhammadShahbaz195/ToolCrush/actions"
    elif command -v open &> /dev/null; then
        open "https://github.com/MuhammadShahbaz195/ToolCrush/actions"
    else
        echo "Please open: https://github.com/MuhammadShahbaz195/ToolCrush/actions"
    fi
fi

echo ""
echo "🎉 Deployment initiated successfully!"
echo ""
echo "📋 Next Steps:"
echo "1. Monitor deployment in GitHub Actions"
echo "2. Wait for deployment to complete (5-10 minutes)"
echo "3. Verify deployment: ./scripts/validate-deployment.sh production"
echo "4. Visit your site: https://toolrapter.com"
echo ""
echo "🔗 Quick Links:"
echo "- Website: https://toolrapter.com"
echo "- Admin: https://toolrapter.com/admin"
echo "- Health Check: https://toolrapter.com/api/health"
echo "- GitHub Actions: https://github.com/MuhammadShahbaz195/ToolCrush/actions"
