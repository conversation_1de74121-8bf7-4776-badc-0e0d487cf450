# 🚀 ToolRapter - Enterprise Next.js 14 Application

[![Next.js](https://img.shields.io/badge/Next.js-15.3.2-black?style=for-the-badge&logo=next.js)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.3.3-blue?style=for-the-badge&logo=typescript)](https://www.typescriptlang.org/)
[![TailwindCSS](https://img.shields.io/badge/Tailwind-3.3.6-38B2AC?style=for-the-badge&logo=tailwind-css)](https://tailwindcss.com/)
[![Deployment](https://img.shields.io/badge/deployment-production%20ready-brightgreen?style=for-the-badge)](https://toolrapter.com)
[![Security](https://img.shields.io/badge/security-enterprise%20grade-blue?style=for-the-badge)](docs/SECURITY.md)
[![VPS](https://img.shields.io/badge/hosting-hostinger%20vps-purple?style=for-the-badge)](docs/VPS_DEPLOYMENT_GUIDE.md)
[![MongoDB](https://img.shields.io/badge/MongoDB-6.16.0-green?style=for-the-badge&logo=mongodb)](https://www.mongodb.com/)
[![License](https://img.shields.io/badge/License-MIT-yellow?style=for-the-badge)](LICENSE)

> **Enterprise-grade Next.js 14 application with admin panel, dynamic tools, blog system, and comprehensive security features.**

## 🌟 Features

### 🔐 **Authentication & Security**
- **JWT Authentication** with httpOnly cookie storage
- **Role-Based Access Control (RBAC)** - Admin, Editor, User roles
- **Google OAuth Integration** for seamless login
- **In-Memory Rate Limiting** with tiered limits (no external dependencies)
- **CSRF Protection** with double-submit cookie pattern
- **Content Security Policy (CSP)** headers
- **Enterprise-grade security headers** via middleware

### 🎨 **Modern UI/UX**
- **Responsive Design** with mobile-first approach
- **Dark Mode Support** with system preference detection
- **Framer Motion Animations** with touch-first interactions
- **shadcn/ui Components** for consistent design system
- **44px Touch Targets** for accessibility compliance
- **<100ms Response Time** for touch interactions

### 🛠️ **Dynamic Tools System**
- **PDF Converters** with client-side processing
- **Advanced Calculators** with real-time computation
- **Dynamic Tool Loading** with SEO optimization
- **Tool Categories** with filtering and search

### 📝 **Complete Blog System**
- **Rich Text Editor** powered by TipTap
- **SEO Optimization** with meta tags and structured data
- **Category Management** with hierarchical organization
- **Comment System** with moderation
- **Pinterest-style Layout** for visual appeal
- **Social Media Integration** for sharing

### 👨‍💼 **Admin Panel**
- **Complete CMS Control** for content management
- **User Management** with role assignment
- **Analytics Dashboard** with performance metrics
- **Contact Management** with email integration
- **SEO Settings** with global configuration
- **System Settings** for application control

## 🚀 Tech Stack

### **Frontend**
- **Next.js 14** - App Router with Server Components
- **TypeScript** - Type-safe development
- **TailwindCSS** - Utility-first styling
- **Framer Motion** - Smooth animations
- **shadcn/ui** - Modern component library
- **React Hook Form** - Form management with validation
- **Zod** - Schema validation

### **Backend**
- **Next.js API Routes** - Serverless functions
- **MongoDB** - Document database with Mongoose ODM
- **NextAuth.js** - Authentication framework
- **Upstash Redis** - Rate limiting and caching
- **Nodemailer** - Email functionality

### **Development & Testing**
- **Jest** - Unit testing framework
- **ESLint** - Code linting
- **Prettier** - Code formatting
- **TypeScript** - Static type checking

### **Deployment & DevOps**
- **Vercel** - Serverless deployment
- **Hostinger VPS** - Traditional server deployment
- **GitHub Actions** - CI/CD pipeline
- **PM2** - Process management
- **Nginx** - Reverse proxy

## 📦 Installation

### **Prerequisites**
- Node.js 18+ 
- MongoDB database
- Redis instance (Upstash recommended)
- Google OAuth credentials (optional)

### **Quick Start**

```bash
# Clone the repository
git clone https://github.com/MuhammadShahbaz195/ToolCrush.git
cd ToolCrush

# Install dependencies
npm install

# Copy environment variables
cp .env.example .env.local

# Configure your environment variables (see Environment Setup below)
# Edit .env.local with your actual values

# Download required fonts
npm run download-fonts

# Start development server
npm run dev
```

Visit [http://localhost:3000](http://localhost:3000) to see the application.

## ⚙️ Environment Setup

Create a `.env.local` file with the following variables:

```env
# Core Application
NODE_ENV=development
NEXT_PUBLIC_BASE_URL=http://localhost:3000

# Authentication
NEXTAUTH_SECRET=your-super-secure-secret-key-minimum-32-characters
NEXTAUTH_URL=http://localhost:3000

# Database
MONGODB_URI=mongodb+srv://username:<EMAIL>/toolcrush

# Rate Limiting (In-Memory - No External Dependencies)
RATE_LIMIT_STORAGE=memory

# OAuth (Optional)
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Email (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=<EMAIL>
```

> **⚠️ Security Note**: Never commit real environment values to version control. Use strong, unique secrets and rotate them regularly.

## 📁 Project Structure

```
src/
├── app/                    # Next.js 14 App Router
│   ├── (auth)/            # Authentication pages
│   ├── admin/             # Admin panel pages
│   ├── api/               # API routes
│   ├── blog/              # Blog system
│   ├── calculators/       # Calculator tools
│   ├── tools/             # PDF and utility tools
│   └── layout.tsx         # Root layout
├── components/            # Reusable UI components
│   ├── ui/               # shadcn/ui components
│   ├── admin/            # Admin-specific components
│   ├── blog/             # Blog components
│   └── tools/            # Tool components
├── lib/                  # Utility libraries
│   ├── auth.ts           # NextAuth configuration
│   ├── db.ts             # Database connection
│   ├── redis.ts          # Redis configuration
│   └── utils.ts          # Helper functions
├── models/               # MongoDB models
├── hooks/                # Custom React hooks
├── types/                # TypeScript type definitions
└── middleware.ts         # Next.js middleware
```

## 🔒 Security Features

### **Rate Limiting**
- **General API**: 100 requests per 15 minutes
- **Contact Forms**: 5 requests per hour
- **Authentication**: 10 requests per 15 minutes
- **Admin Actions**: Custom limits per endpoint

### **Security Headers**
- Content Security Policy (CSP)
- X-Frame-Options (Clickjacking protection)
- X-Content-Type-Options (MIME sniffing protection)
- Referrer-Policy
- Permissions-Policy

### **Data Protection**
- Input validation with Zod schemas
- SQL injection prevention
- XSS protection
- CSRF token validation
- Secure cookie configuration

## 📱 Mobile Optimization

- **Touch-First Design** with 44px minimum touch targets
- **Responsive Breakpoints** for all screen sizes
- **Optimized Animations** with reduced motion support
- **Progressive Enhancement** maintaining desktop functionality
- **Haptic Feedback** for supported devices
- **Swipe Gestures** for navigation

## 🚀 Deployment

### **Vercel (Recommended)**

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy to Vercel
vercel

# Set environment variables in Vercel dashboard
# Configure custom domain (optional)
```

### **Hostinger VPS**

```bash
# Build the application
npm run build

# Start with PM2
pm2 start npm --name "toolcrush" -- start
pm2 save
pm2 startup

# Configure Nginx reverse proxy
# Set up SSL with Certbot
```

See [DEPLOYMENT.md](docs/DEPLOYMENT.md) for detailed deployment instructions.

## 🧪 Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm run test:coverage

# Run specific test suites
npm test -- --testPathPattern=auth
npm test -- --testPathPattern=api
```

### **Test Coverage**
- **Unit Tests**: Component and utility function testing
- **Integration Tests**: API endpoint testing
- **Security Tests**: Authentication and authorization
- **Performance Tests**: Load time and responsiveness

## 📊 Performance Standards

- **Build Time**: < 20 seconds
- **Initial Page Load**: < 5 seconds
- **Touch Response**: < 100ms
- **Security Overhead**: < 50ms
- **Lighthouse Score**: 90+ across all metrics

### **Performance Monitoring**
- Core Web Vitals tracking
- Real User Monitoring (RUM)
- Error tracking with Sentry (optional)
- Performance budgets enforcement

## 🔧 Development Scripts

```bash
# Development
npm run dev              # Start development server
npm run build           # Build for production
npm run start           # Start production server
npm run lint            # Run ESLint
npm run type-check      # Run TypeScript checks

# Database
npm run seed-admin      # Create admin user
npm run migrate         # Run database migrations

# Utilities
npm run download-fonts  # Download required fonts
npm run analyze         # Analyze bundle size
```

## 🌐 API Documentation

### **Authentication Endpoints**
- `POST /api/auth/signin` - User login
- `POST /api/auth/signup` - User registration
- `POST /api/auth/signout` - User logout
- `GET /api/auth/session` - Get current session

### **Admin Endpoints**
- `GET /api/admin/users` - List all users
- `POST /api/admin/users` - Create user
- `PUT /api/admin/users/[id]` - Update user
- `DELETE /api/admin/users/[id]` - Delete user

### **Blog Endpoints**
- `GET /api/blog/posts` - List blog posts
- `POST /api/blog/posts` - Create blog post
- `PUT /api/blog/posts/[id]` - Update blog post
- `DELETE /api/blog/posts/[id]` - Delete blog post

### **Rate Limiting**
All API endpoints are protected with rate limiting:
- **General**: 100 requests per 15 minutes
- **Auth**: 10 requests per 15 minutes
- **Contact**: 5 requests per hour

## 🔍 SEO Features

- **Dynamic Meta Tags** for all pages
- **Open Graph** and Twitter Card support
- **Structured Data** (JSON-LD) for rich snippets
- **XML Sitemap** generation
- **Robots.txt** configuration
- **Canonical URLs** for duplicate content prevention
- **Image Optimization** with Next.js Image component

## 🎯 Browser Support

- **Chrome**: 90+
- **Firefox**: 88+
- **Safari**: 14+
- **Edge**: 90+
- **Mobile Safari**: 14+
- **Chrome Mobile**: 90+

## 🚨 Troubleshooting

### **Common Issues**

**Build Errors**
```bash
# Clear Next.js cache
rm -rf .next
npm run build
```

**Database Connection Issues**
```bash
# Check MongoDB URI format
# Ensure IP whitelist includes your server
# Verify credentials
```

**Rate Limiting Issues**
```bash
# Rate limiting uses in-memory storage
# No external configuration required
# Check application logs for rate limit errors
```

### **Debug Mode**
```bash
# Enable debug logging
DEBUG=* npm run dev

# Check specific modules
DEBUG=next:* npm run dev
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'feat: add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### **Commit Convention**
We use [Conventional Commits](https://www.conventionalcommits.org/):
- `feat:` - New features
- `fix:` - Bug fixes
- `docs:` - Documentation changes
- `style:` - Code style changes
- `refactor:` - Code refactoring
- `test:` - Test additions/modifications
- `chore:` - Maintenance tasks

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) - The React framework for production
- [shadcn/ui](https://ui.shadcn.com/) - Beautiful and accessible components
- [Framer Motion](https://www.framer.com/motion/) - Production-ready motion library
- [TailwindCSS](https://tailwindcss.com/) - Utility-first CSS framework
- [Upstash](https://upstash.com/) - Serverless Redis for rate limiting

---

<div align="center">
  <p>Built with ❤️ by <a href="https://github.com/MuhammadShahbaz195">Muhammad Shahbaz</a></p>
  <p>⭐ Star this repo if you find it helpful!</p>
</div>
