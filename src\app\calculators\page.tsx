'use client';

import Link from "next/link";
import { useTheme } from "@/hooks/useTheme";
import { ALL_CALCULATORS, categoryLabels } from "@/data/calculators";
import { motion } from "framer-motion";
import { FiSearch, FiGrid, FiTrendingUp, FiDollarSign, FiActivity } from "react-icons/fi";
import { useState, useEffect, useMemo, Suspense, lazy } from "react";
import { useSearchParams } from "next/navigation";
import { Calculator } from "lucide-react"; // Import only specific icons needed
import * as LucideIcons from "lucide-react"; // Keep for dynamic icon rendering

// PERFORMANCE FIX: Dynamic imports for heavy components
const UnifiedCard = lazy(() => import("@/components/ui/UnifiedCard"));
const BackButton = lazy(() => import("@/components/ui/BackButton"));
const RecentBlogPosts = lazy(() => import("@/components/home/<USER>"));
const Footer = lazy(() => import("@/components/layout/Footer"));
const Header = lazy(() => import("@/components/layout/Header"));

// Loading components
const ComponentLoading = () => (
  <div className="w-full h-32 bg-muted animate-pulse rounded-lg" />
);

export default function CalculatorsPage(): JSX.Element {
  const { theme } = useTheme();
  const searchParams = useSearchParams();
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredCalculators, setFilteredCalculators] = useState<any[]>([]);
  const [filteredComingSoonCalculators, setFilteredComingSoonCalculators] = useState<any[]>([]);
  const [totalCalculatorCount, setTotalCalculatorCount] = useState(0);
  const [selectedCategory, setSelectedCategory] = useState<string>("all");

  // Memoize calculator separation for better performance
  const { availableCalculators, comingSoonCalculators } = useMemo(() => {
    const available = ALL_CALCULATORS.filter(calc => !calc.comingSoon);
    const upcoming = ALL_CALCULATORS.filter(calc => calc.comingSoon);
    return { availableCalculators: available, comingSoonCalculators: upcoming };
  }, []);

  // Initialize filtered calculators and calculate total calculator count
  useEffect(() => {
    setFilteredCalculators(availableCalculators);
    setFilteredComingSoonCalculators(comingSoonCalculators);
    setTotalCalculatorCount(availableCalculators.length + comingSoonCalculators.length);
  }, [availableCalculators, comingSoonCalculators]);

  // Filter calculators based on search query and category
  useEffect(() => {
    let filtered = availableCalculators;
    let filteredComingSoon = comingSoonCalculators;

    // Filter by category
    if (selectedCategory !== "all") {
      filtered = filtered.filter(calc => calc.category === selectedCategory);
      filteredComingSoon = filteredComingSoon.filter(calc => calc.category === selectedCategory);
    }

    // Filter by search query
    if (searchQuery.trim() !== '') {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(calc =>
        calc.title.toLowerCase().includes(query) ||
        calc.description.toLowerCase().includes(query) ||
        calc.category.toLowerCase().includes(query)
      );
      filteredComingSoon = filteredComingSoon.filter(calc =>
        calc.title.toLowerCase().includes(query) ||
        calc.description.toLowerCase().includes(query) ||
        calc.category.toLowerCase().includes(query)
      );
    }

    setFilteredCalculators(filtered);
    setFilteredComingSoonCalculators(filteredComingSoon);
  }, [searchQuery, selectedCategory, availableCalculators, comingSoonCalculators]);

  // Handle auto-open functionality from dropdown navigation
  useEffect(() => {
    const openParam = searchParams.get("open");
    if (openParam) {
      // Find and scroll to the calculator
      const calculator = ALL_CALCULATORS.find(calc => calc.id === openParam);
      if (calculator) {
        // Set search to find the calculator
        setSearchQuery(calculator.title);
        // Scroll to results after a short delay
        setTimeout(() => {
          document.getElementById('calculators-section')?.scrollIntoView({ behavior: 'smooth' });
        }, 500);
      }
    }
  }, [searchParams]);

  // Get category statistics
  const categoryStats = useMemo(() => {
    const stats: Record<string, number> = {};
    ALL_CALCULATORS.forEach(calc => {
      stats[calc.category] = (stats[calc.category] || 0) + 1;
    });
    return stats;
  }, []);

  return (
    <main className="min-h-screen relative overflow-hidden">
      {/* Header */}
      <Suspense fallback={<ComponentLoading />}>
        <Header />
      </Suspense>

      {/* Back Button */}
      <div className="absolute top-4 left-4 z-10">
        <Suspense fallback={<div className="w-10 h-10 bg-muted animate-pulse rounded-lg" />}>
          <BackButton href="/" />
        </Suspense>
      </div>

      {/* Enhanced Background with Texture */}
      <div className="absolute inset-0 -z-10">
        {theme === 'dark' ? (
          <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-black">
            {/* Texture Pattern for Dark Mode */}
            <div className="absolute inset-0 opacity-20" style={{
              backgroundImage: `radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0)`,
              backgroundSize: '20px 20px'
            }} />
            {/* Additional gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-yellow-900/10 via-transparent to-blue-900/10" />
          </div>
        ) : (
          <div className="absolute inset-0 bg-gradient-to-br from-yellow-50 via-white to-blue-50">
            {/* Subtle texture for Light Mode */}
            <div className="absolute inset-0 opacity-30" style={{
              backgroundImage: `radial-gradient(circle at 1px 1px, rgba(251,191,36,0.1) 1px, transparent 0)`,
              backgroundSize: '24px 24px'
            }} />
          </div>
        )}
      </div>

      {/* Enhanced Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 relative">
        <div className="max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            <motion.div
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="mb-6"
            >
              <Calculator className={`w-16 h-16 mx-auto mb-6 ${
                theme === 'dark' ? 'text-yellow-400' : 'text-yellow-600'
              }`} />
            </motion.div>

            <h1 className="text-5xl md:text-7xl font-bold mb-8 leading-tight">
              <span className={`bg-gradient-to-r ${
                theme === 'dark'
                  ? 'from-yellow-400 via-orange-400 to-red-400'
                  : 'from-yellow-600 via-orange-600 to-red-600'
              } bg-clip-text text-transparent`}>
                All Calculators
              </span>
            </h1>

            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className={`text-xl md:text-2xl mb-12 max-w-4xl mx-auto leading-relaxed ${
                theme === 'dark' ? 'text-gray-300' : 'text-gray-600'
              }`}
            >
              Discover our complete collection of <span className="font-semibold text-yellow-600 dark:text-yellow-400">{totalCalculatorCount}</span> powerful calculators designed to solve your everyday math problems
            </motion.p>
          </motion.div>

          {/* Category Badges */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="flex flex-wrap justify-center gap-4 mb-12"
          >
            <button
              onClick={() => setSelectedCategory("all")}
              className={`flex items-center px-6 py-3 rounded-full border transition-all duration-300 ${
                selectedCategory === "all"
                  ? 'bg-gradient-to-r from-yellow-100 to-yellow-50 dark:from-yellow-900/50 dark:to-yellow-800/30 text-yellow-700 dark:text-yellow-300 border-yellow-300 dark:border-yellow-600'
                  : 'bg-gray-100 dark:bg-gray-800/50 text-gray-600 dark:text-gray-400 border-gray-200 dark:border-gray-700 hover:border-yellow-300 dark:hover:border-yellow-600'
              }`}
            >
              <FiGrid className="mr-2 w-4 h-4" /> All ({totalCalculatorCount})
            </button>

            {Object.entries(categoryStats).map(([category, count]) => {
              const icons: Record<string, any> = {
                finance: FiDollarSign,
                health: FiActivity,
                math: Calculator,
                conversion: FiGrid,
                developer: FiTrendingUp,
              };
              const IconComponent = icons[category] || Calculator;

              return (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`flex items-center px-6 py-3 rounded-full border transition-all duration-300 ${
                    selectedCategory === category
                      ? 'bg-gradient-to-r from-yellow-100 to-yellow-50 dark:from-yellow-900/50 dark:to-yellow-800/30 text-yellow-700 dark:text-yellow-300 border-yellow-300 dark:border-yellow-600'
                      : 'bg-gray-100 dark:bg-gray-800/50 text-gray-600 dark:text-gray-400 border-gray-200 dark:border-gray-700 hover:border-yellow-300 dark:hover:border-yellow-600'
                  }`}
                >
                  <IconComponent className="mr-2 w-4 h-4" />
                  {categoryLabels[category] || category} ({count})
                </button>
              );
            })}
          </motion.div>

          {/* Enhanced Search Bar */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="max-w-3xl mx-auto mb-16"
          >
            <div className="relative group">
              <div className={`absolute inset-0 rounded-3xl blur-xl transition-opacity duration-300 ${
                theme === 'dark'
                  ? 'bg-gradient-to-r from-yellow-500/20 to-orange-500/20 group-hover:opacity-100 opacity-50'
                  : 'bg-gradient-to-r from-yellow-200/50 to-orange-200/50 group-hover:opacity-100 opacity-30'
              }`} />

              <div className="relative">
                <FiSearch className={`absolute left-6 top-1/2 transform -translate-y-1/2 w-6 h-6 transition-colors duration-300 ${
                  theme === 'dark' ? 'text-gray-400 group-hover:text-yellow-400' : 'text-gray-500 group-hover:text-yellow-600'
                }`} />
                <input
                  type="text"
                  placeholder="Search calculators by name or description..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className={`w-full pl-16 pr-6 py-5 text-lg rounded-3xl border-2 transition-all duration-300 focus:outline-none focus:ring-4 ${
                    theme === 'dark'
                      ? 'bg-gray-800/80 border-gray-600 text-white placeholder-gray-400 focus:border-yellow-500 focus:ring-yellow-500/20 backdrop-blur-sm'
                      : 'bg-white/80 border-gray-200 text-gray-900 placeholder-gray-500 focus:border-yellow-500 focus:ring-yellow-500/20 backdrop-blur-sm'
                  }`}
                />
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Calculators Section */}
      <section id="calculators-section" className="py-16 px-4 sm:px-6 lg:px-8 relative">
        <div className="max-w-7xl mx-auto">
          <motion.h2
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-3xl md:text-4xl font-bold mb-12 text-center"
            style={{ color: 'var(--text-primary)' }}
          >
            {selectedCategory === "all" ? "All Calculators" : (categoryLabels[selectedCategory] || selectedCategory)}
          </motion.h2>

          {searchQuery.trim() !== '' && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="mb-8 text-center"
            >
              <p style={{ color: 'var(--text-secondary)' }}>
                {filteredCalculators.length === 0 ? (
                  'No calculators found matching your search.'
                ) : (
                  `Found ${filteredCalculators.length} calculator${filteredCalculators.length === 1 ? '' : 's'} matching "${searchQuery}"`
                )}
              </p>
            </motion.div>
          )}

          {/* Available Calculators Grid */}
          <motion.div
            initial="hidden"
            animate="visible"
            variants={{
              hidden: { opacity: 0 },
              visible: {
                opacity: 1,
                transition: {
                  staggerChildren: 0.05,
                  delayChildren: 0.1
                }
              }
            }}
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8"
          >
            {filteredCalculators.map((calculator, index) => (
              <Suspense key={calculator.id} fallback={<ComponentLoading />}>
                <UnifiedCard
                  id={calculator.id}
                  title={calculator.title}
                  description={calculator.description}
                  icon={calculator.icon}
                  type="calculator"
                  category={calculator.category}
                  popular={calculator.popular}
                  index={index}
                  variant="enhanced"
                />
              </Suspense>
            ))}
          </motion.div>

          {/* Coming Soon Calculators */}
          {filteredComingSoonCalculators.length > 0 && (
            <>
              <motion.h2
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="text-2xl font-bold mt-16 mb-8 text-center"
                style={{ color: 'var(--text-primary)' }}
              >
                Coming Soon
              </motion.h2>

              <motion.div
                initial="hidden"
                animate="visible"
                variants={{
                  hidden: { opacity: 0 },
                  visible: {
                    opacity: 1,
                    transition: {
                      staggerChildren: 0.05,
                      delayChildren: 0.4
                    }
                  }
                }}
                className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8"
              >
                {filteredComingSoonCalculators.map((calculator, index) => (
                  <Suspense key={calculator.id} fallback={<ComponentLoading />}>
                    <UnifiedCard
                      id={calculator.id}
                      title={calculator.title}
                      description={calculator.description}
                      icon={calculator.icon}
                      type="calculator"
                      category={calculator.category}
                      popular={calculator.popular}
                      comingSoon={true}
                      index={index}
                      delay={0.05 * (index % 8) + 0.4}
                      variant="enhanced"
                    />
                  </Suspense>
                ))}
              </motion.div>
            </>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 relative">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className={`p-8 rounded-3xl border ${
              theme === 'dark'
                ? 'bg-gradient-to-br from-gray-800/50 to-gray-900/50 border-gray-700'
                : 'bg-gradient-to-br from-yellow-50 to-orange-50 border-yellow-200'
            }`}
          >
            <h3 className={`text-2xl md:text-3xl font-bold mb-4 ${
              theme === 'dark' ? 'text-yellow-400' : 'text-yellow-600'
            }`}>
              Need a Custom Calculator?
            </h3>
            <p className={`text-lg mb-6 ${
              theme === 'dark' ? 'text-gray-300' : 'text-gray-600'
            }`}>
              Can't find the calculator you need? Let us know and we'll consider adding it to our collection.
            </p>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.98 }}
            >
              <Link
                href="/contact"
                className={`inline-flex items-center px-8 py-4 rounded-full font-semibold transition-all duration-300 ${
                  theme === 'dark'
                    ? 'bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-400 hover:to-orange-400 text-black'
                    : 'bg-gradient-to-r from-yellow-600 to-orange-600 hover:from-yellow-700 hover:to-orange-700 text-white'
                } shadow-lg hover:shadow-xl`}
              >
                Request Calculator
                <Calculator className="ml-2 w-5 h-5" />
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Recent Blog Posts Section */}
      <Suspense fallback={<ComponentLoading />}>
        <RecentBlogPosts limit={3} />
      </Suspense>

      {/* Footer */}
      <Suspense fallback={<ComponentLoading />}>
        <Footer />
      </Suspense>
    </main>
  );
}
