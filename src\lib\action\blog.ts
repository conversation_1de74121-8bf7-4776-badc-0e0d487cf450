'use server';
import connectToDatabase from '@/lib/db';
import BlogPost from '@/models/BlogPost';
import { revalidatePath } from 'next/cache';
import { BlogPostSchema } from '@/lib/validation/blog';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function getBlogPosts(params?: {
  page?: number;
  limit?: number;
  status?: string;
  search?: string;
}) {
  try {
    await connectToDatabase();
    
    const page = params?.page || 1;
    const limit = params?.limit || 10;
    const skip = (page - 1) * limit;

    const query: any = {};
    
    if (params?.status) {
      query.status = params.status;
    }
    
    if (params?.search) {
      query.$or = [
        { title: { $regex: params.search, $options: 'i' } },
        { content: { $regex: params.search, $options: 'i' } },
      ];
    }

    const [posts, total] = await Promise.all([
      BlogPost.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      BlogPost.countDocuments(query)
    ]);

    return {
      data: JSON.parse(JSON.stringify(posts)),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      }
    };
  } catch (error) {
    console.error('Error fetching blog posts:', error);
    return { data: [], pagination: { page: 1, limit: 10, total: 0, totalPages: 0 } };
  }
}

export async function getBlogPostById(id: string) {
  try {
    await connectToDatabase();
    const post = await BlogPost.findById(id).lean();
    return JSON.parse(JSON.stringify(post));
  } catch (error) {
    console.error('Error fetching blog post:', error);
    return null;
  }
}

export async function createUpdateBlogPost(formData: FormData) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) throw new Error('Unauthorized');

    await connectToDatabase();
    
    const validated = BlogPostSchema.parse({
      _id: formData.get('_id'),
      title: formData.get('title'),
      content: formData.get('content'),
      slug: formData.get('slug'),
      status: formData.get('status'),
      scheduledAt: formData.get('scheduledAt'),
    });

    let post;
    if (validated._id) {
      post = await BlogPost.findByIdAndUpdate(validated._id, validated, { new: true });
    } else {
      post = await BlogPost.create({ ...validated, authorId: session.user.id });
    }

    revalidatePath('/admin/blog');
    return JSON.parse(JSON.stringify(post));
  } catch (error) {
    console.error('Error saving blog post:', error);
    return { error: 'Failed to save post' };
  }
}

export async function deleteBlogPost(id: string) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) throw new Error('Unauthorized');

    await connectToDatabase();
    await BlogPost.findByIdAndDelete(id);
    
    revalidatePath('/admin/blog');
    return { success: true };
  } catch (error) {
    console.error('Error deleting blog post:', error);
    return { error: 'Failed to delete post' };
  }
}