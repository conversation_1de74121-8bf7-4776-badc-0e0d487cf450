'use client';

import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { ArrowRight, BookOpen, Users, Star, Zap } from 'lucide-react';
import Link from 'next/link';
import { useTheme } from '@/hooks/useTheme';

export function AboutPlatform() {
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  // const stats = [
  //   { icon: <BookOpen className="h-6 w-6" />, value: '500+', label: 'Articles Published' },
  //   { icon: <Users className="h-6 w-6" />, value: '10K+', label: 'Active Readers' },
  //   { icon: <Star className="h-6 w-6" />, value: '4.9', label: 'Average Rating' },
  //   { icon: <Zap className="h-6 w-6" />, value: '50+', label: 'Tools Available' },
  // ];

  const features = [
    {
      title: 'Expert Insights',
      description: 'Get access to in-depth tutorials and guides written by industry experts.',
      icon: '🎯'
    },
    {
      title: 'Practical Tools',
      description: 'Discover and use powerful tools that enhance your productivity and workflow.',
      icon: '🛠️'
    },
    {
      title: 'Community Driven',
      description: 'Join a thriving community of learners and professionals sharing knowledge.',
      icon: '👥'
    },
    {
      title: 'Always Updated',
      description: 'Stay current with the latest trends, technologies, and best practices.',
      icon: '🔄'
    }
  ];

  return (
    <section className={`py-20 ${isDark ? 'bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900' : 'bg-gradient-to-br from-gray-50 via-white to-gray-50'}`}>
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Left Column - Content */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="space-y-8"
          >
            <div>
              <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-6">
                <BookOpen className="h-4 w-4" />
                About Our Platform
              </div>
              
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                🧠 About the Blog Platform
              </h2>
              
              <p className={`text-lg leading-relaxed mb-8 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                Welcome to our comprehensive blog platform where knowledge meets innovation. We're dedicated to providing 
                you with the latest insights, practical tutorials, and powerful tools to enhance your digital productivity.
              </p>

              <p className={`text-lg leading-relaxed mb-8 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                Whether you're a professional looking to streamline your workflow, a student eager to learn new skills, 
                or an enthusiast exploring the latest technologies, our platform offers something valuable for everyone.
              </p>
            </div>
{/* 
            Stats
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-6"
            >
              {stats.map((stat, index) => (
                <motion.div
                  key={index}
                  whileHover={{ scale: 1.05 }}
                  className={`
                    text-center p-4 rounded-xl border
                    ${isDark 
                      ? 'bg-gray-800/50 border-gray-700' 
                      : 'bg-white border-gray-200'
                    }
                  `}
                >
                  <div className={`flex justify-center mb-2 ${isDark ? 'text-primary' : 'text-primary'}`}>
                    {stat.icon}
                  </div>
                  <div className={`text-2xl font-bold mb-1 ${isDark ? 'text-white' : 'text-gray-900'}`}>
                    {stat.value}
                  </div>
                  <div className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                    {stat.label}
                  </div>
                </motion.div>
              ))}
            </motion.div> */}

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="flex flex-col sm:flex-row gap-4"
            >
              <Link href="/blog/all">
                <Button size="lg" className="rounded-full px-8 py-6 text-lg group">
                  Explore All Articles
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                </Button>
              </Link>
              
              <Link href="/tools">
                <Button 
                  size="lg" 
                  variant="outline" 
                  className="rounded-full px-8 py-6 text-lg group"
                >
                  Try Our Tools
                  <Zap className="ml-2 h-5 w-5 group-hover:scale-110 transition-transform duration-300" />
                </Button>
              </Link>
            </motion.div>
          </motion.div>

          {/* Right Column - Features */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="space-y-6"
          >
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{ 
                  x: 8,
                  transition: { duration: 0.2 }
                }}
                className={`
                  p-6 rounded-2xl border transition-all duration-300 hover:shadow-lg
                  ${isDark 
                    ? 'bg-gray-800/50 border-gray-700 hover:border-gray-600' 
                    : 'bg-white border-gray-200 hover:border-gray-300'
                  }
                `}
              >
                <div className="flex items-start gap-4">
                  <motion.div
                    whileHover={{ 
                      scale: 1.2,
                      rotate: 10
                    }}
                    transition={{ duration: 0.2 }}
                    className="text-3xl"
                  >
                    {feature.icon}
                  </motion.div>
                  
                  <div className="flex-1">
                    <h3 className={`text-xl font-bold mb-2 ${isDark ? 'text-white' : 'text-gray-900'}`}>
                      {feature.title}
                    </h3>
                    <p className={`${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                      {feature.description}
                    </p>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Bottom CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className={`
            mt-20 p-8 rounded-3xl text-center
            ${isDark 
              ? 'bg-gradient-to-r from-primary/20 to-secondary/20 border border-primary/30' 
              : 'bg-gradient-to-r from-primary/10 to-secondary/10 border border-primary/20'
            }
          `}
        >
          <h3 className={`text-2xl md:text-3xl font-bold mb-4 ${isDark ? 'text-white' : 'text-gray-900'}`}>
            Ready to Start Your Journey?
          </h3>
          <p className={`text-lg mb-6 max-w-2xl mx-auto ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
            Join thousands of readers who trust our platform for the latest insights, tutorials, and tools.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/register">
              <Button size="lg" className="rounded-full px-8 py-6 text-lg">
                Get Started Free
              </Button>
            </Link>
            
            <Link href="/contact">
              <Button 
                size="lg" 
                variant="outline" 
                className="rounded-full px-8 py-6 text-lg"
              >
                Contact Us
              </Button>
            </Link>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
