#!/bin/bash

# =============================================================================
# TOOLCRUSH - DEPLOYMENT SCRIPT
# =============================================================================
# Automated deployment script for Hostinger VPS
# Usage: ./scripts/deploy.sh [environment] [options]

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="toolcrush"
APP_DIR="/var/www/$APP_NAME"
BACKUP_DIR="/var/backups/$APP_NAME"
NGINX_SITE="/etc/nginx/sites-available/$APP_NAME"
LOG_FILE="/var/log/deploy-$(date +%Y%m%d-%H%M%S).log"

# Default values
ENVIRONMENT="production"
SKIP_BACKUP=false
SKIP_TESTS=false
FORCE_DEPLOY=false

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Help function
show_help() {
    cat << EOF
ToolCrush Deployment Script

Usage: $0 [environment] [options]

Environments:
    production    Deploy to production (default)
    staging       Deploy to staging

Options:
    --skip-backup     Skip creating backup
    --skip-tests      Skip running tests
    --force           Force deployment without checks
    --help           Show this help message

Examples:
    $0 production
    $0 staging --skip-tests
    $0 production --force

EOF
}

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        production|staging)
            ENVIRONMENT="$1"
            shift
            ;;
        --skip-backup)
            SKIP_BACKUP=true
            shift
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --force)
            FORCE_DEPLOY=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            error "Unknown option: $1"
            ;;
    esac
done

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   error "This script must be run as root (use sudo)"
fi

# Start deployment
log "🚀 Starting deployment to $ENVIRONMENT environment"
log "📝 Deployment log: $LOG_FILE"

# Pre-deployment checks
if [[ "$FORCE_DEPLOY" != true ]]; then
    log "🔍 Running pre-deployment checks..."
    
    # Check if Node.js is installed
    if ! command -v node &> /dev/null; then
        error "Node.js is not installed"
    fi
    
    # Check if PM2 is installed
    if ! command -v pm2 &> /dev/null; then
        error "PM2 is not installed"
    fi
    
    # Check if Nginx is installed
    if ! command -v nginx &> /dev/null; then
        error "Nginx is not installed"
    fi
    
    success "✅ Pre-deployment checks passed"
fi

# Create backup
if [[ "$SKIP_BACKUP" != true ]] && [[ -d "$APP_DIR" ]]; then
    log "📦 Creating backup..."
    mkdir -p "$BACKUP_DIR"
    BACKUP_NAME="backup-$(date +%Y%m%d-%H%M%S)"
    cp -r "$APP_DIR" "$BACKUP_DIR/$BACKUP_NAME"
    success "✅ Backup created: $BACKUP_DIR/$BACKUP_NAME"
fi

# Prepare application directory
log "📁 Preparing application directory..."
mkdir -p "$APP_DIR"
cd "$APP_DIR"

# Clone or update repository
if [[ ! -d ".git" ]]; then
    log "📥 Cloning repository..."
    git clone https://github.com/MuhammadShahbaz195/ToolCrush.git .
else
    log "🔄 Updating repository..."
    git fetch origin
    if [[ "$ENVIRONMENT" == "production" ]]; then
        git checkout main
        git pull origin main
    else
        git checkout develop
        git pull origin develop
    fi
fi

# Install dependencies
log "📦 Installing dependencies..."
npm ci --production=false --prefer-offline

# Download fonts
log "🔤 Downloading fonts..."
npm run download-fonts

# Run tests
if [[ "$SKIP_TESTS" != true ]] && [[ "$FORCE_DEPLOY" != true ]]; then
    log "🧪 Running tests..."
    npm test
    success "✅ Tests passed"
fi

# Build application
log "🏗️ Building application..."
NODE_ENV=production npm run build
success "✅ Application built successfully"

# Set proper permissions
log "🔐 Setting permissions..."
chown -R www-data:www-data "$APP_DIR"
chmod -R 755 "$APP_DIR"

# Configure PM2
log "⚙️ Configuring PM2..."
if pm2 list | grep -q "$APP_NAME"; then
    log "🔄 Restarting existing PM2 process..."
    pm2 restart "$APP_NAME"
else
    log "🆕 Starting new PM2 process..."
    pm2 start ecosystem.config.js --env "$ENVIRONMENT"
fi

# Save PM2 configuration
pm2 save

# Configure Nginx
if [[ ! -f "$NGINX_SITE" ]]; then
    log "⚙️ Configuring Nginx..."
    cp nginx.conf "$NGINX_SITE"
    ln -sf "$NGINX_SITE" /etc/nginx/sites-enabled/
    
    # Test Nginx configuration
    if nginx -t; then
        systemctl reload nginx
        success "✅ Nginx configured and reloaded"
    else
        error "❌ Nginx configuration test failed"
    fi
else
    log "🔄 Reloading Nginx..."
    systemctl reload nginx
fi

# Health check
log "🏥 Performing health check..."
sleep 10

if curl -f http://localhost:3000/api/health &> /dev/null; then
    success "✅ Health check passed"
else
    error "❌ Health check failed"
fi

# SSL Certificate (Let's Encrypt)
if [[ "$ENVIRONMENT" == "production" ]] && [[ ! -f "/etc/letsencrypt/live/toolcrush.com/fullchain.pem" ]]; then
    log "🔒 Setting up SSL certificate..."
    warning "⚠️ Please run: certbot --nginx -d toolcrush.com -d www.toolcrush.com"
fi

# Cleanup old backups (keep last 5)
if [[ -d "$BACKUP_DIR" ]]; then
    log "🧹 Cleaning up old backups..."
    cd "$BACKUP_DIR"
    ls -t | tail -n +6 | xargs -r rm -rf
fi

# Final status
log "📊 Deployment Summary:"
log "   Environment: $ENVIRONMENT"
log "   Application: $APP_NAME"
log "   Directory: $APP_DIR"
log "   PM2 Status: $(pm2 list | grep "$APP_NAME" | awk '{print $10}')"
log "   Nginx Status: $(systemctl is-active nginx)"

success "🎉 Deployment completed successfully!"
log "🌐 Application should be available at: https://toolcrush.com"

# Show next steps
cat << EOF

📋 Next Steps:
1. Verify the application is working: https://toolcrush.com
2. Check PM2 status: pm2 status
3. Monitor logs: pm2 logs $APP_NAME
4. Set up SSL if not done: certbot --nginx -d toolcrush.com
5. Configure monitoring and backups

📚 Useful Commands:
- View logs: pm2 logs $APP_NAME
- Restart app: pm2 restart $APP_NAME
- Nginx reload: systemctl reload nginx
- Check status: pm2 status && systemctl status nginx

EOF
