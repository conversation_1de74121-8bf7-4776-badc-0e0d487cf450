import { renderHook, act } from '@testing-library/react'
import { useTouch, hapticFeedback } from '../useTouch'

// Mock navigator.vibrate
Object.defineProperty(navigator, 'vibrate', {
  writable: true,
  value: jest.fn(),
})

describe('useTouch', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should initialize with default state', () => {
    const { result } = renderHook(() => useTouch({}, {}))
    
    expect(result.current.touchState).toEqual({
      isPressed: false,
      isLongPressed: false,
      isSwiping: false,
      swipeDirection: null,
      touchStartTime: null,
    })
  })

  it('should detect touch device correctly', () => {
    // Mock touch support
    Object.defineProperty(window, 'ontouchstart', {
      writable: true,
      value: {},
    })

    const { result } = renderHook(() => useTouch({}, {}))
    expect(result.current.isTouchDevice).toBe(true)
  })

  it('should provide touch handlers', () => {
    const { result } = renderHook(() => useTouch({}, {}))
    
    expect(result.current.touchHandlers).toHaveProperty('onTouchStart')
    expect(result.current.touchHandlers).toHaveProperty('onTouchEnd')
    expect(typeof result.current.touchHandlers.onTouchStart).toBe('function')
    expect(typeof result.current.touchHandlers.onTouchEnd).toBe('function')
  })
})

describe('hapticFeedback', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should call vibrate for light feedback', () => {
    hapticFeedback.light()
    expect(navigator.vibrate).toHaveBeenCalledWith(10)
  })

  it('should call vibrate for medium feedback', () => {
    hapticFeedback.medium()
    expect(navigator.vibrate).toHaveBeenCalledWith(20)
  })

  it('should call vibrate for heavy feedback', () => {
    hapticFeedback.heavy()
    expect(navigator.vibrate).toHaveBeenCalledWith([30, 10, 30])
  })

  it('should call vibrate for success feedback', () => {
    hapticFeedback.success()
    expect(navigator.vibrate).toHaveBeenCalledWith([50, 25, 50])
  })

  it('should call vibrate for error feedback', () => {
    hapticFeedback.error()
    expect(navigator.vibrate).toHaveBeenCalledWith([100, 50, 100, 50, 100])
  })
})
