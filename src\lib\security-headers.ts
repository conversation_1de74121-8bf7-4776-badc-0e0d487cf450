import { NextRequest, NextResponse } from 'next/server';

// Security headers configuration
export interface SecurityHeadersConfig {
  contentSecurityPolicy?: string;
  frameOptions?: string;
  contentTypeOptions?: string;
  referrerPolicy?: string;
  permissionsPolicy?: string;
  strictTransportSecurity?: string;
  xssProtection?: string;
  crossOriginEmbedderPolicy?: string;
  crossOriginOpenerPolicy?: string;
  crossOriginResourcePolicy?: string;
  originAgentCluster?: string;
  server?: string;
}

// Default security headers configuration
export const DEFAULT_SECURITY_HEADERS: SecurityHeadersConfig = {
  // Content Security Policy - Allow existing resources
  contentSecurityPolicy: [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://accounts.google.com https://apis.google.com",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com data:",
    "img-src 'self' data: blob: https: http:",
    "media-src 'self' data: blob:",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'",
    "connect-src 'self' https://accounts.google.com https://api.cloudinary.com https://res.cloudinary.com",
    "worker-src 'self' blob:",
    "child-src 'self' blob:",
    "manifest-src 'self'",
    "upgrade-insecure-requests"
  ].join('; '),
  
  // Prevent framing
  frameOptions: 'DENY',
  
  // Prevent MIME type sniffing
  contentTypeOptions: 'nosniff',
  
  // Referrer policy
  referrerPolicy: 'strict-origin-when-cross-origin',
  
  // Permissions policy - restrict unnecessary browser features
  permissionsPolicy: [
    'camera=()',
    'microphone=()',
    'geolocation=()',
    'interest-cohort=()',
    'payment=()',
    'usb=()',
    'bluetooth=()',
    'magnetometer=()',
    'gyroscope=()',
    'accelerometer=()',
    'ambient-light-sensor=()',
    'autoplay=(self)',
    'encrypted-media=(self)',
    'fullscreen=(self)',
    'picture-in-picture=(self)'
  ].join(', '),
  
  // HSTS for HTTPS (only in production)
  strictTransportSecurity: process.env.NODE_ENV === 'production' 
    ? 'max-age=********; includeSubDomains; preload' 
    : undefined,
  
  // XSS Protection (legacy but still useful)
  xssProtection: '1; mode=block',

  // Cross-Origin Embedder Policy
  crossOriginEmbedderPolicy: 'require-corp',

  // Cross-Origin Opener Policy
  crossOriginOpenerPolicy: 'same-origin',

  // Cross-Origin Resource Policy
  crossOriginResourcePolicy: 'same-origin',

  // Origin Agent Cluster
  originAgentCluster: '?1',

  // Server identification (security through obscurity)
  server: 'ToolBox',
};

// Development-specific CSP (more permissive)
export const DEVELOPMENT_CSP = [
  "default-src 'self'",
  "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://accounts.google.com https://apis.google.com",
  "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
  "font-src 'self' https://fonts.gstatic.com data:",
  "img-src 'self' data: blob: https: http: localhost:*",
  "media-src 'self' data: blob:",
  "object-src 'none'",
  "base-uri 'self'",
  "form-action 'self'",
  "frame-ancestors 'none'",
  "connect-src 'self' https://accounts.google.com https://api.cloudinary.com https://res.cloudinary.com ws: wss: localhost:*",
  "worker-src 'self' blob:",
  "child-src 'self' blob:",
  "manifest-src 'self'"
].join('; ');

// Get environment-specific security headers
export function getSecurityHeaders(): SecurityHeadersConfig {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  return {
    ...DEFAULT_SECURITY_HEADERS,
    contentSecurityPolicy: isDevelopment 
      ? DEVELOPMENT_CSP 
      : DEFAULT_SECURITY_HEADERS.contentSecurityPolicy,
    // Remove HSTS in development
    strictTransportSecurity: isDevelopment 
      ? undefined 
      : DEFAULT_SECURITY_HEADERS.strictTransportSecurity,
  };
}

// Apply security headers to response
export function applySecurityHeaders(
  response: NextResponse, 
  config?: Partial<SecurityHeadersConfig>
): NextResponse {
  const headers = { ...getSecurityHeaders(), ...config };
  
  // Apply each header if defined
  if (headers.contentSecurityPolicy) {
    response.headers.set('Content-Security-Policy', headers.contentSecurityPolicy);
  }
  
  if (headers.frameOptions) {
    response.headers.set('X-Frame-Options', headers.frameOptions);
  }
  
  if (headers.contentTypeOptions) {
    response.headers.set('X-Content-Type-Options', headers.contentTypeOptions);
  }
  
  if (headers.referrerPolicy) {
    response.headers.set('Referrer-Policy', headers.referrerPolicy);
  }
  
  if (headers.permissionsPolicy) {
    response.headers.set('Permissions-Policy', headers.permissionsPolicy);
  }
  
  if (headers.strictTransportSecurity) {
    response.headers.set('Strict-Transport-Security', headers.strictTransportSecurity);
  }
  
  if (headers.xssProtection) {
    response.headers.set('X-XSS-Protection', headers.xssProtection);
  }

  if (headers.crossOriginEmbedderPolicy) {
    response.headers.set('Cross-Origin-Embedder-Policy', headers.crossOriginEmbedderPolicy);
  }

  if (headers.crossOriginOpenerPolicy) {
    response.headers.set('Cross-Origin-Opener-Policy', headers.crossOriginOpenerPolicy);
  }

  if (headers.crossOriginResourcePolicy) {
    response.headers.set('Cross-Origin-Resource-Policy', headers.crossOriginResourcePolicy);
  }

  if (headers.originAgentCluster) {
    response.headers.set('Origin-Agent-Cluster', headers.originAgentCluster);
  }

  if (headers.server) {
    response.headers.set('Server', headers.server);
  }

  return response;
}

// Route-specific CSP overrides
export function getRouteSpecificCSP(pathname: string): string | undefined {
  // Admin routes might need additional permissions
  if (pathname.startsWith('/admin')) {
    return [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://accounts.google.com https://apis.google.com",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "font-src 'self' https://fonts.gstatic.com data:",
      "img-src 'self' data: blob: https: http:",
      "media-src 'self' data: blob:",
      "object-src 'none'",
      "base-uri 'self'",
      "form-action 'self'",
      "frame-ancestors 'none'",
      "connect-src 'self' https://accounts.google.com https://api.cloudinary.com https://res.cloudinary.com",
      "worker-src 'self' blob:",
      "child-src 'self' blob:",
      "manifest-src 'self'"
    ].join('; ');
  }
  
  // Blog editor might need additional permissions for rich text editing
  if (pathname.includes('/blog/edit') || pathname.includes('/blog/create')) {
    return [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://accounts.google.com https://apis.google.com",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "font-src 'self' https://fonts.gstatic.com data:",
      "img-src 'self' data: blob: https: http:",
      "media-src 'self' data: blob:",
      "object-src 'none'",
      "base-uri 'self'",
      "form-action 'self'",
      "frame-ancestors 'none'",
      "connect-src 'self' https://accounts.google.com https://api.cloudinary.com https://res.cloudinary.com",
      "worker-src 'self' blob:",
      "child-src 'self' blob:",
      "manifest-src 'self'"
    ].join('; ');
  }
  
  return undefined;
}

// Check if route should have relaxed CSP
export function shouldRelaxCSP(pathname: string): boolean {
  const relaxedRoutes = [
    '/admin',
    '/blog/edit',
    '/blog/create',
    '/tools',
    '/calculators'
  ];
  
  return relaxedRoutes.some(route => pathname.startsWith(route));
}

// Security headers for API routes
export function getAPISecurityHeaders(): Record<string, string> {
  const headers: Record<string, string> = {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
    'Cross-Origin-Embedder-Policy': 'require-corp',
    'Cross-Origin-Opener-Policy': 'same-origin',
    'Cross-Origin-Resource-Policy': 'same-origin',
    'Origin-Agent-Cluster': '?1',
    'Server': 'ToolBox',
  };

  // Add HSTS in production
  if (process.env.NODE_ENV === 'production') {
    headers['Strict-Transport-Security'] = 'max-age=********; includeSubDomains; preload';
  }

  return headers;
}

// Apply security headers specifically for API responses
export function applyAPISecurityHeaders(response: NextResponse): NextResponse {
  const headers = getAPISecurityHeaders();
  
  Object.entries(headers).forEach(([key, value]) => {
    response.headers.set(key, value);
  });
  
  return response;
}

// Nonce generation for CSP (if needed for inline scripts) - Edge Runtime compatible
export function generateNonce(): string {
  const randomBytes = crypto.getRandomValues(new Uint8Array(16));
  return btoa(String.fromCharCode(...Array.from(randomBytes)));
}

// CSP with nonce support (for future use)
export function getCSPWithNonce(nonce: string): string {
  return [
    "default-src 'self'",
    `script-src 'self' 'nonce-${nonce}' https://accounts.google.com https://apis.google.com`,
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com data:",
    "img-src 'self' data: blob: https: http:",
    "media-src 'self' data: blob:",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'",
    "connect-src 'self' https://accounts.google.com https://api.cloudinary.com https://res.cloudinary.com",
    "worker-src 'self' blob:",
    "child-src 'self' blob:",
    "manifest-src 'self'"
  ].join('; ');
}

// Validate CSP configuration
export function validateCSPConfig(csp: string): boolean {
  try {
    // Basic validation - check for required directives
    const requiredDirectives = ['default-src', 'script-src', 'style-src'];
    const hasRequired = requiredDirectives.every(directive => 
      csp.includes(directive)
    );
    
    return hasRequired;
  } catch (error) {
    console.error('CSP validation error:', error);
    return false;
  }
}
