// Migration script to assign categories to existing blog posts
const mongoose = require('mongoose');

// Database connection
const connectToDatabase = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/toolbox');
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('Database connection error:', error);
    process.exit(1);
  }
};

// Define schemas
const categorySchema = new mongoose.Schema({
  name: String,
  slug: String,
  description: String,
  count: { type: Number, default: 0 }
}, { timestamps: true });

const blogPostSchema = new mongoose.Schema({
  title: String,
  content: String,
  slug: String,
  description: String,
  tags: [String],
  categoryId: { type: mongoose.Schema.Types.ObjectId, ref: 'Category' },
  status: String,
  visibility: String,
  featuredImage: String,
  imageCredit: String,
  publishedAt: Date,
  authorId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }
}, { timestamps: true });

// Get or create models
const Category = mongoose.models.Category || mongoose.model('Category', categorySchema);
const BlogPost = mongoose.models.BlogPost || mongoose.model('BlogPost', blogPostSchema);

// Migration function
const migrateBlogCategories = async () => {
  try {
    console.log('Starting blog category migration...');

    // Get all categories
    const categories = await Category.find();
    console.log(`Found ${categories.length} categories`);

    if (categories.length === 0) {
      console.log('No categories found. Creating default categories...');
      
      const defaultCategories = [
        { name: 'Technology', slug: 'technology', description: 'Technology related posts' },
        { name: 'Health', slug: 'health', description: 'Health and wellness posts' },
        { name: 'General', slug: 'general', description: 'General posts' }
      ];

      for (const cat of defaultCategories) {
        await Category.create(cat);
      }
      
      console.log('Created default categories');
      // Refresh categories list
      const newCategories = await Category.find();
      categories.push(...newCategories);
    }

    // Get all blog posts without categories
    const postsWithoutCategories = await BlogPost.find({
      $or: [
        { categoryId: null },
        { categoryId: { $exists: false } }
      ]
    });

    console.log(`Found ${postsWithoutCategories.length} posts without categories`);

    if (postsWithoutCategories.length === 0) {
      console.log('All posts already have categories assigned');
      return;
    }

    // Assign categories based on content/title keywords
    const categoryKeywords = {
      'Technology': ['tech', 'technology', 'software', 'code', 'programming', 'development', 'app', 'web', 'digital'],
      'Health': ['health', 'medical', 'fitness', 'wellness', 'diet', 'exercise', 'nutrition'],
      'General': [] // fallback category
    };

    let updatedCount = 0;

    for (const post of postsWithoutCategories) {
      let assignedCategory = null;
      const postText = (post.title + ' ' + post.content + ' ' + (post.tags || []).join(' ')).toLowerCase();

      // Find matching category based on keywords
      for (const [categoryName, keywords] of Object.entries(categoryKeywords)) {
        if (categoryName === 'General') continue; // Skip general for now
        
        const hasKeyword = keywords.some(keyword => postText.includes(keyword));
        if (hasKeyword) {
          const category = categories.find(cat => cat.name === categoryName);
          if (category) {
            assignedCategory = category._id;
            break;
          }
        }
      }

      // If no category found, assign to General
      if (!assignedCategory) {
        const generalCategory = categories.find(cat => cat.name === 'General');
        if (generalCategory) {
          assignedCategory = generalCategory._id;
        }
      }

      // Update the post
      if (assignedCategory) {
        await BlogPost.findByIdAndUpdate(post._id, { categoryId: assignedCategory });
        updatedCount++;
        console.log(`Updated post "${post.title}" with category`);
      }
    }

    console.log(`Migration completed. Updated ${updatedCount} posts.`);

    // Update category counts
    console.log('Updating category counts...');
    for (const category of categories) {
      const count = await BlogPost.countDocuments({
        categoryId: category._id,
        status: 'published',
        visibility: 'public'
      });
      
      await Category.findByIdAndUpdate(category._id, { count });
      console.log(`Category "${category.name}" has ${count} posts`);
    }

    console.log('Category counts updated successfully');

  } catch (error) {
    console.error('Migration error:', error);
  }
};

// Run migration
const runMigration = async () => {
  await connectToDatabase();
  await migrateBlogCategories();
  await mongoose.connection.close();
  console.log('Migration completed and database connection closed');
};

// Export for use in other scripts or run directly
if (require.main === module) {
  runMigration();
}

module.exports = { migrateBlogCategories, runMigration };
