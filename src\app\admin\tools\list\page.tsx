"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { MoreHorizontal, Pencil, Eye, BarChart2, Loader2 } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { ALL_TOOLS } from "@/data/tools";
import Link from "next/link";
import { toast } from "@/hooks/use-toast";
import { RequireRole } from "@/components/auth/RequireRole";
import { useSession } from "next-auth/react";

interface Tool {
  id: string;
  title: string;
  description: string;
  icon: string;
  category: string;
  popular: boolean;
  active: boolean;
  usageCount: number;
}

export default function ToolsListPage() {
  const { data: session } = useSession();
  const [searchTerm, setSearchTerm] = useState("");
  const [tools, setTools] = useState<Tool[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTools = async () => {
      try {
        setLoading(true);
        // In a real app, this would be an API call
        // Simulate API call with a delay
        await new Promise(resolve => setTimeout(resolve, 800));

        // Transform the tools data to include active status and usage count
        const toolsWithStats = ALL_TOOLS.map(tool => ({
          ...tool,
          active: true,
          usageCount: Math.floor(Math.random() * 10000),
          popular: tool.popular || false,
        }));

        setTools(toolsWithStats);
        setError(null);
      } catch (err) {
        console.error("Error fetching tools:", err);
        setError("Failed to load tools data. Please try again.");
        toast({
          title: "Error",
          description: "Failed to load tools data. Please try again.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchTools();
  }, []);

  const filteredTools = tools.filter(tool =>
    tool.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    tool.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const toggleToolStatus = (id: string) => {
    setTools(tools.map(tool =>
      tool.id === id ? { ...tool, active: !tool.active } : tool
    ));

    toast({
      title: "Tool status updated",
      description: `Tool status has been ${tools.find(t => t.id === id)?.active ? 'disabled' : 'enabled'}.`,
    });
  };

  const getCategoryBadge = (category: string) => {
    switch (category) {
      case "pdf":
        return <Badge className="bg-blue-500">PDF</Badge>;
      case "office":
        return <Badge className="bg-green-500">Office</Badge>;
      case "image":
        return <Badge className="bg-purple-500">Image</Badge>;
      default:
        return <Badge variant="outline">{category}</Badge>;
    }
  };

  return (
    <RequireRole role="admin">
      {loading ? (
        <div className="flex items-center justify-center h-[60vh]">
          <div className="text-center">
            <Loader2 className="h-12 w-12 animate-spin mx-auto text-[rgb(var(--primary))]" />
            <p className="mt-4 text-lg text-adaptive-muted">Loading tools data...</p>
          </div>
        </div>
      ) : error ? (
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <div className="text-center">
            <p className="text-lg text-[rgb(var(--destructive))]">Failed to load tools data</p>
            <p className="mt-2 text-adaptive-muted">Please try again later or contact support</p>
            <Button onClick={() => window.location.reload()} className="mt-4">
              Retry
            </Button>
          </div>
        </div>
      ) : (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="w-full max-w-sm">
          <Input
            placeholder="Search tools..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Button asChild>
          <Link href="/admin/tools/stats">
            <BarChart2 className="mr-2 h-4 w-4" />
            Usage Statistics
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Tools ({tools.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Tool</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Usage</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTools.length > 0 ? (
                  filteredTools.map((tool) => (
                    <TableRow key={tool.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div className="text-2xl">{tool.icon}</div>
                          <div>
                            <div className="font-medium">{tool.title}</div>
                            <div className="text-sm text-adaptive-muted">{tool.description}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{getCategoryBadge(tool.category)}</TableCell>
                      <TableCell>{tool.usageCount.toLocaleString()}</TableCell>
                      <TableCell>
                        <Switch
                          checked={tool.active}
                          onCheckedChange={() => toggleToolStatus(tool.id)}
                        />
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Actions</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem asChild>
                              <Link href={`/tools/${tool.id}`} target="_blank">
                                <Eye className="mr-2 h-4 w-4" />
                                View Tool
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link href={`/admin/tools/edit/${tool.id}`}>
                                <Pencil className="mr-2 h-4 w-4" />
                                Edit Settings
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link href={`/admin/tools/stats/${tool.id}`}>
                                <BarChart2 className="mr-2 h-4 w-4" />
                                View Stats
                              </Link>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-6 text-muted-foreground">
                      No tools found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
      )}
    </RequireRole>
  );
}
