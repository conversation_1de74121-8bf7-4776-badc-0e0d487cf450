#!/bin/bash

# =============================================================================
# GITHUB SECRETS VALIDATION SCRIPT
# =============================================================================
# This script helps validate that all required GitHub secrets are properly
# configured for the ToolCrush deployment pipeline.
#
# Usage: bash scripts/validate-secrets.sh
# =============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Required secrets
REQUIRED_SECRETS=(
    "VPS_SSH_KEY"
    "VPS_USER" 
    "VPS_HOST"
    "MONGODB_URI"
    "NEXTAUTH_SECRET"
    "GOOGLE_CLIENT_ID"
    "GOOGLE_CLIENT_SECRET"
    "UPSTASH_REDIS_REST_URL"
    "UPSTASH_REDIS_REST_TOKEN"
)

echo -e "${BLUE}🔐 GitHub Secrets Validation for ToolCrush${NC}"
echo "=============================================="
echo ""

# Function to print status
print_status() {
    local status=$1
    local message=$2
    
    if [ "$status" = "success" ]; then
        echo -e "${GREEN}✅ $message${NC}"
    elif [ "$status" = "warning" ]; then
        echo -e "${YELLOW}⚠️  $message${NC}"
    elif [ "$status" = "error" ]; then
        echo -e "${RED}❌ $message${NC}"
    else
        echo -e "${BLUE}ℹ️  $message${NC}"
    fi
}

# Function to check if GitHub CLI is available
check_gh_cli() {
    if command -v gh &> /dev/null; then
        if gh auth status &> /dev/null; then
            return 0
        else
            print_status "warning" "GitHub CLI is installed but not authenticated"
            return 1
        fi
    else
        print_status "warning" "GitHub CLI is not installed"
        return 1
    fi
}

# Function to validate secret format
validate_secret_format() {
    local secret_name=$1
    local secret_value=$2
    
    case $secret_name in
        "VPS_SSH_KEY")
            if [[ $secret_value == *"BEGIN OPENSSH PRIVATE KEY"* ]]; then
                return 0
            else
                print_status "error" "VPS_SSH_KEY: Invalid SSH private key format"
                return 1
            fi
            ;;
        "MONGODB_URI")
            if [[ $secret_value == mongodb+srv://* ]]; then
                return 0
            else
                print_status "error" "MONGODB_URI: Should start with 'mongodb+srv://'"
                return 1
            fi
            ;;
        "NEXTAUTH_SECRET")
            if [ ${#secret_value} -ge 32 ]; then
                return 0
            else
                print_status "error" "NEXTAUTH_SECRET: Should be at least 32 characters long"
                return 1
            fi
            ;;
        "UPSTASH_REDIS_REST_URL")
            if [[ $secret_value == https://* ]]; then
                return 0
            else
                print_status "error" "UPSTASH_REDIS_REST_URL: Should start with 'https://'"
                return 1
            fi
            ;;
        *)
            if [ -n "$secret_value" ]; then
                return 0
            else
                print_status "error" "$secret_name: Empty value"
                return 1
            fi
            ;;
    esac
}

# Function to test SSH connection
test_ssh_connection() {
    local vps_user=$1
    local vps_host=$2
    local ssh_key_path=$3
    
    print_status "info" "Testing SSH connection to $vps_user@$vps_host..."
    
    if [ -f "$ssh_key_path" ]; then
        if ssh -i "$ssh_key_path" -o ConnectTimeout=10 -o StrictHostKeyChecking=no "$vps_user@$vps_host" "echo 'SSH connection successful'" 2>/dev/null; then
            print_status "success" "SSH connection to VPS successful"
            return 0
        else
            print_status "error" "SSH connection to VPS failed"
            return 1
        fi
    else
        print_status "warning" "SSH private key file not found at $ssh_key_path"
        return 1
    fi
}

# Function to test MongoDB connection
test_mongodb_connection() {
    local mongodb_uri=$1
    
    print_status "info" "Testing MongoDB connection..."
    
    # Use mongosh if available, otherwise skip
    if command -v mongosh &> /dev/null; then
        if mongosh "$mongodb_uri" --eval "db.runCommand('ping')" &> /dev/null; then
            print_status "success" "MongoDB connection successful"
            return 0
        else
            print_status "error" "MongoDB connection failed"
            return 1
        fi
    else
        print_status "warning" "mongosh not available, skipping MongoDB connection test"
        return 1
    fi
}

# Function to test Redis connection
test_redis_connection() {
    local redis_url=$1
    local redis_token=$2
    
    print_status "info" "Testing Upstash Redis connection..."
    
    # Test Redis connection using curl
    local response=$(curl -s -w "%{http_code}" -H "Authorization: Bearer $redis_token" "$redis_url/ping" -o /dev/null)
    
    if [ "$response" = "200" ]; then
        print_status "success" "Upstash Redis connection successful"
        return 0
    else
        print_status "error" "Upstash Redis connection failed (HTTP $response)"
        return 1
    fi
}

# Main validation function
main() {
    local has_errors=false
    
    print_status "info" "Starting GitHub secrets validation..."
    echo ""
    
    # Check if GitHub CLI is available
    if check_gh_cli; then
        print_status "success" "GitHub CLI is available and authenticated"
        
        # Check each required secret
        for secret in "${REQUIRED_SECRETS[@]}"; do
            print_status "info" "Checking secret: $secret"
            
            # Try to get secret (this will only work if you have admin access)
            if gh secret list | grep -q "$secret"; then
                print_status "success" "$secret is configured"
            else
                print_status "error" "$secret is missing"
                has_errors=true
            fi
        done
    else
        print_status "warning" "Cannot validate secrets without GitHub CLI authentication"
        print_status "info" "Please install and authenticate GitHub CLI to validate secrets"
        print_status "info" "Run: gh auth login"
    fi
    
    echo ""
    print_status "info" "Manual validation steps:"
    echo ""
    
    # Manual validation instructions
    echo "1. Go to: https://github.com/MuhammadShahbaz195/ToolCrush/settings/secrets/actions"
    echo "2. Verify all required secrets are listed:"
    for secret in "${REQUIRED_SECRETS[@]}"; do
        echo "   - $secret"
    done
    
    echo ""
    print_status "info" "Additional validation (if secrets are available locally):"
    
    # Check for local SSH key
    if [ -f "$HOME/.ssh/toolcrush_ed25519" ]; then
        print_status "success" "Local SSH private key found"
        
        # Test SSH connection if VPS details are available
        if [ -n "$VPS_USER" ] && [ -n "$VPS_HOST" ]; then
            test_ssh_connection "$VPS_USER" "$VPS_HOST" "$HOME/.ssh/toolcrush_ed25519"
        fi
    else
        print_status "warning" "Local SSH private key not found at ~/.ssh/toolcrush_ed25519"
    fi
    
    # Test MongoDB connection if URI is available
    if [ -n "$MONGODB_URI" ]; then
        test_mongodb_connection "$MONGODB_URI"
    fi
    
    # Test Redis connection if credentials are available
    if [ -n "$UPSTASH_REDIS_REST_URL" ] && [ -n "$UPSTASH_REDIS_REST_TOKEN" ]; then
        test_redis_connection "$UPSTASH_REDIS_REST_URL" "$UPSTASH_REDIS_REST_TOKEN"
    fi
    
    echo ""
    if [ "$has_errors" = true ]; then
        print_status "error" "Validation completed with errors"
        print_status "info" "Please review the GitHub Secrets Setup Guide: docs/GITHUB_SECRETS_SETUP.md"
        exit 1
    else
        print_status "success" "Validation completed successfully"
        print_status "info" "Your GitHub secrets appear to be properly configured"
    fi
}

# Run main function
main "$@"
