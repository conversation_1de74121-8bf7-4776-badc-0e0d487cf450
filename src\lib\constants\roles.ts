export const USER_ROLES = {
  ADMIN: 'admin',
  EDITOR: 'editor',
  MODERATOR: 'moderator',
  USER: 'user'
} as const;

export type UserRole = typeof USER_ROLES[keyof typeof USER_ROLES];

export const ROLE_PERMISSIONS = {
  [USER_ROLES.ADMIN]: {
    canManageUsers: true,
    canManagePosts: true,
    canManageTools: true,
    canManageSettings: true,
    canViewAnalytics: true,
    canUploadFiles: true,
    canDeletePosts: true,
    canPublishPosts: true,
  },
  [USER_ROLES.EDITOR]: {
    canManageUsers: false,
    canManagePosts: true,
    canManageTools: false,
    canManageSettings: false,
    canViewAnalytics: false,
    canUploadFiles: true,
    canDeletePosts: false,
    canPublishPosts: true,
  },
  [USER_ROLES.MODERATOR]: {
    canManageUsers: false,
    canManagePosts: true,
    canManageTools: false,
    canManageSettings: false,
    canViewAnalytics: false,
    canUploadFiles: true,
    canDeletePosts: false,
    canPublishPosts: false,
  },
  [USER_ROLES.USER]: {
    canManageUsers: false,
    canManagePosts: false,
    canManageTools: false,
    canManageSettings: false,
    canViewAnalytics: false,
    canUploadFiles: false,
    canDeletePosts: false,
    canPublishPosts: false,
  },
} as const;

export function hasPermission(role: UserRole, permission: keyof typeof ROLE_PERMISSIONS[typeof USER_ROLES.ADMIN]): boolean {
  return ROLE_PERMISSIONS[role]?.[permission] ?? false;
}

export function isAdmin(role: UserRole): boolean {
  return role === USER_ROLES.ADMIN;
}

export function canAccessAdmin(role: UserRole): boolean {
  return [USER_ROLES.ADMIN, USER_ROLES.EDITOR, USER_ROLES.MODERATOR].includes(role as typeof USER_ROLES.ADMIN | typeof USER_ROLES.EDITOR | typeof USER_ROLES.MODERATOR);
}
