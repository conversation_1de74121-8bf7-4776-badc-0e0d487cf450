import { NextResponse } from 'next/server';
import {
  applySecurityHeaders,
  getSecurityHeaders,
  getAPISecurityHeaders,
  getRouteSpecificCSP,
  applyAPISecurityHeaders,
  DEFAULT_SECURITY_HEADERS,
} from '../security-headers';

// Mock NextResponse
jest.mock('next/server', () => ({
  NextResponse: {
    next: jest.fn(() => ({
      headers: new Map(),
    })),
  },
}));

describe('Security Headers', () => {
  let mockResponse: any;

  beforeEach(() => {
    mockResponse = {
      headers: new Map(),
    };
    mockResponse.headers.set = jest.fn();
  });

  describe('getSecurityHeaders', () => {
    it('should return default security headers for production', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      const headers = getSecurityHeaders();

      expect(headers).toHaveProperty('contentSecurityPolicy');
      expect(headers).toHaveProperty('frameOptions', 'DENY');
      expect(headers).toHaveProperty('contentTypeOptions', 'nosniff');
      expect(headers).toHaveProperty('strictTransportSecurity');

      process.env.NODE_ENV = originalEnv;
    });

    it('should return development headers for development', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      const headers = getSecurityHeaders();

      expect(headers).toHaveProperty('contentSecurityPolicy');
      expect(headers.strictTransportSecurity).toBeUndefined();

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('applySecurityHeaders', () => {
    it('should apply all security headers to response', () => {
      const response = applySecurityHeaders(mockResponse);

      expect(mockResponse.headers.set).toHaveBeenCalledWith(
        'Content-Security-Policy',
        expect.any(String)
      );
      expect(mockResponse.headers.set).toHaveBeenCalledWith(
        'X-Frame-Options',
        'DENY'
      );
      expect(mockResponse.headers.set).toHaveBeenCalledWith(
        'X-Content-Type-Options',
        'nosniff'
      );
    });

    it('should apply custom security headers when provided', () => {
      const customConfig = {
        frameOptions: 'SAMEORIGIN',
        contentTypeOptions: 'nosniff',
      };

      applySecurityHeaders(mockResponse, customConfig);

      expect(mockResponse.headers.set).toHaveBeenCalledWith(
        'X-Frame-Options',
        'SAMEORIGIN'
      );
    });

    it('should skip undefined headers', () => {
      const customConfig = {
        frameOptions: undefined,
        contentTypeOptions: 'nosniff',
      };

      applySecurityHeaders(mockResponse, customConfig);

      expect(mockResponse.headers.set).not.toHaveBeenCalledWith(
        'X-Frame-Options',
        expect.any(String)
      );
    });
  });

  describe('getAPISecurityHeaders', () => {
    it('should return API-specific security headers', () => {
      const headers = getAPISecurityHeaders();

      expect(headers).toHaveProperty('X-Content-Type-Options', 'nosniff');
      expect(headers).toHaveProperty('X-Frame-Options', 'DENY');
      expect(headers).toHaveProperty('X-XSS-Protection', '1; mode=block');
      expect(headers).toHaveProperty('Server', 'ToolBox');
    });

    it('should include HSTS in production', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      const headers = getAPISecurityHeaders();

      expect(headers).toHaveProperty('Strict-Transport-Security');

      process.env.NODE_ENV = originalEnv;
    });

    it('should not include HSTS in development', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      const headers = getAPISecurityHeaders();

      expect(headers).not.toHaveProperty('Strict-Transport-Security');

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('getRouteSpecificCSP', () => {
    it('should return admin CSP for admin routes', () => {
      const csp = getRouteSpecificCSP('/admin/dashboard');
      expect(csp).toContain('script-src');
      expect(csp).toContain('style-src');
    });

    it('should return blog CSP for blog routes', () => {
      const csp = getRouteSpecificCSP('/blog/post-title');
      expect(csp).toContain('img-src');
      expect(csp).toContain('media-src');
    });

    it('should return tools CSP for tools routes', () => {
      const csp = getRouteSpecificCSP('/tools/pdf-merger');
      expect(csp).toContain('worker-src');
      expect(csp).toContain('blob:');
    });

    it('should return null for unknown routes', () => {
      const csp = getRouteSpecificCSP('/unknown-route');
      expect(csp).toBeNull();
    });
  });

  describe('applyAPISecurityHeaders', () => {
    it('should apply API security headers to response', () => {
      applyAPISecurityHeaders(mockResponse);

      expect(mockResponse.headers.set).toHaveBeenCalledWith(
        'X-Content-Type-Options',
        'nosniff'
      );
      expect(mockResponse.headers.set).toHaveBeenCalledWith(
        'X-Frame-Options',
        'DENY'
      );
      expect(mockResponse.headers.set).toHaveBeenCalledWith(
        'Server',
        'ToolBox'
      );
    });
  });

  describe('DEFAULT_SECURITY_HEADERS', () => {
    it('should have required security headers', () => {
      expect(DEFAULT_SECURITY_HEADERS).toHaveProperty('contentSecurityPolicy');
      expect(DEFAULT_SECURITY_HEADERS).toHaveProperty('frameOptions');
      expect(DEFAULT_SECURITY_HEADERS).toHaveProperty('contentTypeOptions');
      expect(DEFAULT_SECURITY_HEADERS).toHaveProperty('referrerPolicy');
    });

    it('should have proper CSP directives', () => {
      const csp = DEFAULT_SECURITY_HEADERS.contentSecurityPolicy;
      expect(csp).toContain("default-src 'self'");
      expect(csp).toContain("object-src 'none'");
      expect(csp).toContain("base-uri 'self'");
      expect(csp).toContain("form-action 'self'");
    });
  });
});
