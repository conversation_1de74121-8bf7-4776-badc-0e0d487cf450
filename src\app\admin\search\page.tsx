"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  Search, 
  FileText, 
  Users, 
  MessageSquare,
  Eye,
  Edit,
  Trash2,
  Calendar,
  User,
  Filter
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface SearchResult {
  id: string;
  type: "post" | "user" | "comment";
  title: string;
  content: string;
  author?: string;
  date: string;
  status?: string;
  url: string;
}

export default function SearchPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("all");
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const { toast } = useToast();

  useEffect(() => {
    // Load recent searches from localStorage
    const saved = localStorage.getItem("admin-recent-searches");
    if (saved) {
      setRecentSearches(JSON.parse(saved));
    }
  }, []);

  const performSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`/api/admin/search?q=${encodeURIComponent(query)}`);
      if (response.ok) {
        const data = await response.json();
        setSearchResults(data.results || []);
        
        // Add to recent searches
        const updated = [query, ...recentSearches.filter(s => s !== query)].slice(0, 5);
        setRecentSearches(updated);
        localStorage.setItem("admin-recent-searches", JSON.stringify(updated));
      } else {
        throw new Error("Search failed");
      }
    } catch (error) {
      console.error("Search error:", error);
      toast({
        title: "Error",
        description: "Failed to perform search",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    performSearch(searchQuery);
  };

  const clearRecentSearches = () => {
    setRecentSearches([]);
    localStorage.removeItem("admin-recent-searches");
  };

  const getFilteredResults = () => {
    if (activeTab === "all") return searchResults;
    return searchResults.filter(result => result.type === activeTab);
  };

  const getResultIcon = (type: string) => {
    switch (type) {
      case "post":
        return <FileText className="h-4 w-4" />;
      case "user":
        return <Users className="h-4 w-4" />;

      default:
        return <Search className="h-4 w-4" />;
    }
  };

  const getResultBadge = (type: string, status?: string) => {
    if (status) {
      const variants = {
        draft: "secondary",
        published: "default",
        pending: "outline",
        approved: "default",
        rejected: "destructive"
      } as const;

      return (
        <Badge variant={variants[status as keyof typeof variants] || "secondary"}>
          {status}
        </Badge>
      );
    }

    const typeColors = {
      post: "bg-blue-100 text-blue-800",
      user: "bg-green-100 text-green-800"
    } as const;

    return (
      <Badge className={typeColors[type as keyof typeof typeColors] || "bg-gray-100 text-gray-800"}>
        {type}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric"
    });
  };

  return (
    <div className="container mx-auto p-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-8"
      >
        <h1 className="text-3xl font-bold text-foreground flex items-center gap-2 mb-2">
          <Search className="h-8 w-8" />
          Admin Search
        </h1>
        <p className="text-muted-foreground">
          Search across posts, users, comments, and other content
        </p>
      </motion.div>

      {/* Search Form */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="mb-8"
      >
        <Card>
          <CardContent className="p-6">
            <form onSubmit={handleSearch} className="space-y-4">
              <div className="flex gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Search for posts, users, comments..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Searching...
                    </>
                  ) : (
                    "Search"
                  )}
                </Button>
              </div>

              {/* Recent Searches */}
              {recentSearches.length > 0 && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">Recent Searches</label>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={clearRecentSearches}
                    >
                      Clear
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {recentSearches.map((search, index) => (
                      <button
                        key={index}
                        type="button"
                        onClick={() => {
                          setSearchQuery(search);
                          performSearch(search);
                        }}
                        className="px-3 py-1 text-sm bg-muted hover:bg-muted/80 rounded-full transition-colors"
                      >
                        {search}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </form>
          </CardContent>
        </Card>
      </motion.div>

      {/* Search Results */}
      {searchResults.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Search Results ({searchResults.length})</span>
                <div className="text-sm text-muted-foreground">
                  for "{searchQuery}"
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="all">
                    All ({searchResults.length})
                  </TabsTrigger>
                  <TabsTrigger value="post">
                    Posts ({searchResults.filter(r => r.type === "post").length})
                  </TabsTrigger>
                  <TabsTrigger value="user">
                    Users ({searchResults.filter(r => r.type === "user").length})
                  </TabsTrigger>
                  <TabsTrigger value="comment">
                    Comments ({searchResults.filter(r => r.type === "comment").length})
                  </TabsTrigger>
                </TabsList>

                <TabsContent value={activeTab} className="mt-6">
                  <div className="space-y-4">
                    {getFilteredResults().map((result, index) => (
                      <motion.div
                        key={result.id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.2, delay: index * 0.05 }}
                        className="border rounded-lg p-4 hover:shadow-sm transition-shadow"
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              {getResultIcon(result.type)}
                              <h3 className="font-medium">{result.title}</h3>
                              {getResultBadge(result.type, result.status)}
                            </div>
                            
                            <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                              {result.content}
                            </p>

                            <div className="flex items-center gap-4 text-xs text-muted-foreground">
                              {result.author && (
                                <div className="flex items-center gap-1">
                                  <User className="h-3 w-3" />
                                  {result.author}
                                </div>
                              )}
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                {formatDate(result.date)}
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center gap-2 ml-4">
                            <Link href={result.url}>
                              <Button variant="ghost" size="sm">
                                <Eye className="h-4 w-4" />
                              </Button>
                            </Link>
                            {result.type === "post" && (
                              <Link href={`/admin/blog/edit/${result.id}`}>
                                <Button variant="ghost" size="sm">
                                  <Edit className="h-4 w-4" />
                                </Button>
                              </Link>
                            )}
                            {result.type === "user" && (
                              <Link href={`/admin/users/edit/${result.id}`}>
                                <Button variant="ghost" size="sm">
                                  <Edit className="h-4 w-4" />
                                </Button>
                              </Link>
                            )}
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* No Results */}
      {searchQuery && searchResults.length === 0 && !isLoading && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card>
            <CardContent className="text-center py-12">
              <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No results found</h3>
              <p className="text-muted-foreground">
                No content found matching "{searchQuery}". Try different keywords or check your spelling.
              </p>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Search Tips */}
      {!searchQuery && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>Search Tips</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">What you can search for:</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Blog post titles and content</li>
                    <li>• User names and email addresses</li>
                    <li>• Comment content and authors</li>
                    <li>• Tags and categories</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Search tips:</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Use quotes for exact phrases</li>
                    <li>• Search is case-insensitive</li>
                    <li>• Use filters to narrow results</li>
                    <li>• Recent searches are saved for quick access</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  );
}
