#!/bin/bash

# =============================================================================
# TOOLRAPTER - VPS DEPLOYMENT SCRIPT
# =============================================================================
# Enterprise-grade deployment script for Hostinger VPS
# Domain: toolrapter.com | VPS: ************
# Usage: ./scripts/deploy-vps.sh [production|staging]

set -e  # Exit on any error

# =============================================================================
# CONFIGURATION
# =============================================================================
ENVIRONMENT=${1:-production}
VPS_HOST="************"
VPS_USER="root"
APP_NAME="toolrapter"
DOMAIN="toolrapter.com"
REPO_URL="https://github.com/MuhammadShahbaz195/ToolCrush.git"

# Paths
APP_DIR="/var/www/$APP_NAME"
BACKUP_DIR="/var/backups/$APP_NAME"
LOG_DIR="/var/log/pm2"
NGINX_SITES="/etc/nginx/sites-available"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# =============================================================================
# HELPER FUNCTIONS
# =============================================================================
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_ssh_connection() {
    log_info "Testing SSH connection to VPS..."
    if ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no $VPS_USER@$VPS_HOST "echo 'SSH connection successful'" > /dev/null 2>&1; then
        log_success "SSH connection established"
    else
        log_error "Failed to connect to VPS. Please check your SSH configuration."
        exit 1
    fi
}

create_backup() {
    log_info "Creating backup of current deployment..."
    ssh $VPS_USER@$VPS_HOST << EOF
        if [ -d "$APP_DIR" ]; then
            sudo mkdir -p "$BACKUP_DIR"
            TIMESTAMP=\$(date +%Y%m%d_%H%M%S)
            sudo tar -czf "$BACKUP_DIR/backup_\$TIMESTAMP.tar.gz" -C "$APP_DIR" . 2>/dev/null || true
            
            # Keep only last 5 backups
            sudo find "$BACKUP_DIR" -name "backup_*.tar.gz" -type f | sort -r | tail -n +6 | sudo xargs rm -f
            
            echo "Backup created: backup_\$TIMESTAMP.tar.gz"
        else
            echo "No existing deployment to backup"
        fi
EOF
}

deploy_application() {
    log_info "Deploying application to VPS..."
    
    # Build application locally
    log_info "Building application locally..."
    npm ci --production=false
    npm run download-fonts
    npm run build
    
    # Create deployment package
    log_info "Creating deployment package..."
    tar -czf deployment.tar.gz \
        .next/ \
        public/ \
        package.json \
        package-lock.json \
        next.config.js \
        scripts/ \
        ecosystem.config.js \
        --exclude=node_modules
    
    # Upload to VPS
    log_info "Uploading deployment package to VPS..."
    scp -o StrictHostKeyChecking=no deployment.tar.gz $VPS_USER@$VPS_HOST:/tmp/
    
    # Deploy on VPS
    log_info "Extracting and configuring application on VPS..."
    ssh $VPS_USER@$VPS_HOST << EOF
        # Create application directory
        sudo mkdir -p "$APP_DIR"
        sudo mkdir -p "$LOG_DIR"
        
        # Extract deployment
        cd "$APP_DIR"
        sudo tar -xzf /tmp/deployment.tar.gz
        
        # Install production dependencies
        sudo npm ci --production --prefer-offline
        
        # Set proper permissions
        sudo chown -R www-data:www-data "$APP_DIR"
        sudo chmod -R 755 "$APP_DIR"
        
        # Clean up
        rm -f /tmp/deployment.tar.gz
EOF
    
    # Clean up local files
    rm -f deployment.tar.gz
}

restart_application() {
    log_info "Restarting application with PM2..."
    ssh $VPS_USER@$VPS_HOST << EOF
        # Restart application with PM2
        if pm2 list | grep -q "$APP_NAME"; then
            pm2 reload "$APP_NAME" --update-env
        else
            pm2 start ecosystem.config.js --env $ENVIRONMENT
        fi
        
        # Save PM2 configuration
        pm2 save
        
        # Reload Nginx
        sudo nginx -t && sudo systemctl reload nginx
EOF
}

health_check() {
    log_info "Performing health check..."
    
    # Wait for application to start
    sleep 30
    
    # Check if application is responding
    if ssh $VPS_USER@$VPS_HOST "curl -f --max-time 30 http://localhost:3000/api/health" > /dev/null 2>&1; then
        log_success "Application health check passed"
    else
        log_error "Application health check failed"
        return 1
    fi
    
    # Check external access
    if curl -f --max-time 30 "https://$DOMAIN/api/health" > /dev/null 2>&1; then
        log_success "External health check passed"
    else
        log_warning "External health check failed - check SSL/DNS configuration"
    fi
}

rollback_deployment() {
    log_warning "Rolling back to previous deployment..."
    ssh $VPS_USER@$VPS_HOST << EOF
        LATEST_BACKUP=\$(sudo find "$BACKUP_DIR" -name "backup_*.tar.gz" -type f | sort -r | head -n 1)
        
        if [ -n "\$LATEST_BACKUP" ]; then
            echo "Rolling back to: \$(basename \$LATEST_BACKUP)"
            
            # Extract backup
            cd "$APP_DIR"
            sudo tar -xzf "\$LATEST_BACKUP"
            
            # Restart application
            pm2 reload "$APP_NAME" --update-env
            
            echo "Rollback completed successfully"
        else
            echo "No backup found for rollback"
            exit 1
        fi
EOF
}

# =============================================================================
# MAIN DEPLOYMENT PROCESS
# =============================================================================
main() {
    log_info "Starting deployment to $ENVIRONMENT environment..."
    log_info "Target: $VPS_USER@$VPS_HOST"
    log_info "Domain: $DOMAIN"
    
    # Pre-deployment checks
    check_ssh_connection
    
    # Create backup
    create_backup
    
    # Deploy application
    if deploy_application; then
        log_success "Application deployed successfully"
    else
        log_error "Deployment failed"
        exit 1
    fi
    
    # Restart services
    if restart_application; then
        log_success "Application restarted successfully"
    else
        log_error "Failed to restart application"
        exit 1
    fi
    
    # Health check
    if health_check; then
        log_success "Deployment completed successfully!"
        log_info "Application is live at: https://$DOMAIN"
    else
        log_error "Health check failed. Rolling back..."
        rollback_deployment
        exit 1
    fi
}

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # Validate environment
    if [[ "$ENVIRONMENT" != "production" && "$ENVIRONMENT" != "staging" ]]; then
        log_error "Invalid environment. Use 'production' or 'staging'"
        exit 1
    fi
    
    # Check if required tools are installed
    for tool in ssh scp curl npm tar; do
        if ! command -v $tool &> /dev/null; then
            log_error "$tool is required but not installed"
            exit 1
        fi
    done
    
    # Run main deployment
    main
fi

# =============================================================================
# USAGE EXAMPLES
# =============================================================================
# Deploy to production:
# ./scripts/deploy-vps.sh production
#
# Deploy to staging:
# ./scripts/deploy-vps.sh staging
#
# Make script executable:
# chmod +x scripts/deploy-vps.sh
