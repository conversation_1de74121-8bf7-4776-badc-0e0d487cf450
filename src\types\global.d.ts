// global.d.ts - Global type definitions for ToolCrush
import { MongoClient } from 'mongodb';
import * as mongoose from 'mongoose';

// Module declarations for external packages
declare module 'bcrypt' {
    import * as bcrypt from 'bcryptjs';
    export = bcrypt;
}

declare module 'framer-motion' {
    export * from 'framer-motion/dist/index';
}

declare module 'lucide-react' {
    export * from 'lucide-react/dist/index';
}

declare module 'next/link' {
    export * from 'next/dist/client/link';
}

declare global {
    // For Mongoose
    var mongoose: {
      conn: mongoose.Connection | null;
      promise: Promise<mongoose.Connection> | null;
    };

    // For native MongoDB driver
    var mongoClient: {
      client: MongoClient | null;
      promise: Promise<MongoClient> | null;
    };

    // JSX namespace for React components
    namespace JSX {
        interface IntrinsicElements {
            [elemName: string]: any;
        }
    }
}