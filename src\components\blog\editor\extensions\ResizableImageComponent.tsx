'use client';

import React, { useState, useEffect, useRef } from 'react';
import { NodeViewWrapper, NodeViewProps } from '@tiptap/react';

// Define image size options
type ImageSizeOption = 'ES' | 'S' | 'M' | 'L' | 'XL' | 'XXL' | 'custom';

interface ResizableImageComponentProps extends NodeViewProps {
  node: NodeViewProps['node'] & {
    attrs: {
      src?: string;
      alt?: string;
      title?: string;
      width?: number;
      height?: number;
      alignment?: 'left' | 'center' | 'right';
      sizeOption?: ImageSizeOption;
    };
  };
}

export const ResizableImageComponent: React.FC<ResizableImageComponentProps> = (props) => {
  const { node, updateAttributes, selected } = props;
  const [size, setSize] = useState({
    width: node.attrs.width,
    height: node.attrs.height,
  });
  const [resizing, setResizing] = useState(false);
  const [initialSize, setInitialSize] = useState({ width: 0, height: 0 });
  const [initialPosition, setInitialPosition] = useState({ x: 0, y: 0 });
  const [sizeOption, setSizeOption] = useState<ImageSizeOption>(node.attrs.sizeOption || 'custom');
  const imageRef = useRef<HTMLImageElement>(null);
  const naturalSizeRef = useRef<{ width: number, height: number }>({ width: 0, height: 0 });

  // Set initial size when image loads - using a ref to track if we've already set initial size
  const initialSizeSetRef = useRef(false);

  useEffect(() => {
    if (imageRef.current && (!node.attrs.width || !node.attrs.height) && !initialSizeSetRef.current) {
      const img = imageRef.current;
      initialSizeSetRef.current = true;

      updateAttributes({
        width: img.naturalWidth,
        height: img.naturalHeight,
      });

      setSize({
        width: img.naturalWidth,
        height: img.naturalHeight,
      });
    }
  }, [node.attrs.src, updateAttributes]);

  // Update size state when node attributes change
  useEffect(() => {
    if (node.attrs.width && node.attrs.height && !resizing) {
      setSize({
        width: node.attrs.width,
        height: node.attrs.height,
      });
    }
  }, [node.attrs.width, node.attrs.height, resizing]);

  // Handle image load without useCallback to avoid dependency issues
  const handleImageLoad = () => {
    if (imageRef.current) {
      const img = imageRef.current;

      // Store natural dimensions for size calculations
      naturalSizeRef.current = {
        width: img.naturalWidth,
        height: img.naturalHeight,
      };

      // Only update if we don't have dimensions yet
      if (!size.width || !size.height) {
        const newSize = {
          width: img.naturalWidth,
          height: img.naturalHeight,
        };
        setSize(newSize);

        // Only update attributes if needed
        if (!node.attrs.width || !node.attrs.height) {
          updateAttributes({
            ...newSize,
            sizeOption: 'custom'
          });
        }
      }

      // Apply size option if it's already set and we haven't applied it yet
      if (node.attrs.sizeOption &&
          node.attrs.sizeOption !== 'custom' &&
          sizeOption !== node.attrs.sizeOption) {
        applySizeOption(node.attrs.sizeOption);
      }
    }
  };

  // Function to calculate size based on size option
  const applySizeOption = (option: ImageSizeOption) => {
    // Skip if already at this size option or missing required refs
    if (option === sizeOption || !imageRef.current || !naturalSizeRef.current.width) return;

    const naturalWidth = naturalSizeRef.current.width;
    const naturalHeight = naturalSizeRef.current.height;
    const aspectRatio = naturalWidth / naturalHeight;

    let newWidth: number;

    // Calculate new width based on size option
    switch (option) {
      case 'ES':
        newWidth = 150; // Extra Small - 150px
        break;
      case 'S':
        newWidth = 300; // Small - 300px
        break;
      case 'M':
        newWidth = 500; // Medium - 500px
        break;
      case 'L':
        newWidth = 800; // Large - 800px
        break;
      case 'XL':
        newWidth = 1024; // Extra Large - 1024px
        break;
      case 'XXL':
        newWidth = Math.min(1280, window.innerWidth * 0.9); // XX Large - 1280px, max 90% of viewport
        break;
      default:
        return; // Don't change for custom
    }

    // Calculate height maintaining aspect ratio
    const newHeight = Math.round(newWidth / aspectRatio);

    // Update size state and attributes
    const newSize = { width: newWidth, height: newHeight };
    setSize(newSize);
    setSizeOption(option);

    updateAttributes({
      ...node.attrs,
      ...newSize,
      sizeOption: option
    });

    // Ensure alignment is maintained after size change
    setTimeout(() => {
      const wrapper = imageRef.current?.closest('.resizable-image-wrapper') as HTMLElement;
      if (wrapper && node.attrs.alignment) {
        wrapper.setAttribute('data-alignment', node.attrs.alignment);
        wrapper.style.textAlign = node.attrs.alignment;
      }
    }, 0);
  };

  // Ensure alignment is properly applied when component mounts or alignment changes
  useEffect(() => {
    const wrapper = imageRef.current?.closest('.resizable-image-wrapper') as HTMLElement;
    if (wrapper && node.attrs.alignment) {
      wrapper.setAttribute('data-alignment', node.attrs.alignment);
      wrapper.style.textAlign = node.attrs.alignment;
    }
  }, [node.attrs.alignment]);

  const startResize = (e: React.MouseEvent, corner: string) => {
    e.preventDefault();
    e.stopPropagation();

    if (!imageRef.current) return;

    setResizing(true);
    const currentSize = {
      width: size.width || imageRef.current.width,
      height: size.height || imageRef.current.height,
    };
    setInitialSize(currentSize);
    setInitialPosition({ x: e.clientX, y: e.clientY });

    const handleMouseMove = (e: MouseEvent) => {
      e.preventDefault();

      if (corner === 'br') {
        // Bottom right corner - maintain aspect ratio
        const widthChange = e.clientX - initialPosition.x;
        const aspectRatio = currentSize.width / currentSize.height;
        const newWidth = Math.max(100, currentSize.width + widthChange);
        const newHeight = Math.round(newWidth / aspectRatio);

        setSize({ width: newWidth, height: newHeight });
      }
    };

    const handleMouseUp = () => {
      setResizing(false);

      // Get the final size after resizing
      const finalSize = {
        width: size.width,
        height: size.height
      };

      // When manually resizing, set size option to 'custom'
      setSizeOption('custom');

      // Only update attributes if size has changed
      if (finalSize.width !== node.attrs.width || finalSize.height !== node.attrs.height) {
        updateAttributes({
          ...node.attrs,
          width: finalSize.width,
          height: finalSize.height,
          sizeOption: 'custom'
        });

        // Ensure alignment is maintained after resize
        setTimeout(() => {
          const wrapper = imageRef.current?.closest('.resizable-image-wrapper') as HTMLElement;
          if (wrapper && node.attrs.alignment) {
            wrapper.setAttribute('data-alignment', node.attrs.alignment);
            wrapper.style.textAlign = node.attrs.alignment;
          }
        }, 0);
      }

      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  const handleAlignmentChange = (alignment: 'left' | 'center' | 'right') => {
    // Only update if alignment has changed
    if (alignment !== node.attrs.alignment) {
      updateAttributes({
        ...node.attrs,
        alignment
      });

      // Force re-render by updating the wrapper element
      setTimeout(() => {
        const wrapper = imageRef.current?.closest('.resizable-image-wrapper') as HTMLElement;
        if (wrapper) {
          wrapper.setAttribute('data-alignment', alignment);
          wrapper.style.textAlign = alignment;
        }
      }, 0);
    }
  };

  return (
    <NodeViewWrapper
      className="resizable-image-wrapper"
      data-alignment={node.attrs.alignment || 'center'}
      style={{
        display: 'block',
        width: '100%',
        textAlign: node.attrs.alignment || 'center',
        margin: '1rem 0'
      }}
    >
      <div
        className={`image-container ${selected ? 'selected' : ''}`}
        style={{
          position: 'relative',
          display: 'inline-block',
          maxWidth: '100%',
          textAlign: node.attrs.alignment || 'center'
        }}
      >
        <img
          ref={imageRef}
          src={node.attrs.src}
          alt={node.attrs.alt || ''}
          title={node.attrs.title || ''}
          width={size.width}
          height={size.height}
          onLoad={handleImageLoad}
          style={{
            maxWidth: '100%',
            height: 'auto',
            display: 'block',
          }}
        />

        {selected && (
          <>
            {/* Resize handle */}
            <div
              className="resize-handle resize-handle-br"
              onMouseDown={(e) => startResize(e, 'br')}
              style={{
                position: 'absolute',
                bottom: '-6px',
                right: '-6px',
                width: '12px',
                height: '12px',
                background: 'var(--primary)',
                border: '2px solid white',
                borderRadius: '50%',
                cursor: 'nwse-resize',
                zIndex: 10,
              }}
            />

            {/* Alignment controls */}
            <div
              className="alignment-controls"
              style={{
                position: 'absolute',
                top: '-30px',
                left: '50%',
                transform: 'translateX(-50%)',
                display: 'flex',
                gap: '4px',
                background: 'var(--background)',
                border: '1px solid var(--border)',
                borderRadius: '4px',
                padding: '2px',
                zIndex: 10,
              }}
            >
              <button
                type="button"
                onClick={() => handleAlignmentChange('left')}
                className={`alignment-button ${node.attrs.alignment === 'left' ? 'active' : ''}`}
                style={{
                  padding: '2px 4px',
                  background: node.attrs.alignment === 'left' ? 'var(--primary)' : 'transparent',
                  color: node.attrs.alignment === 'left' ? 'white' : 'inherit',
                  border: 'none',
                  borderRadius: '2px',
                  cursor: 'pointer',
                }}
              >
                L
              </button>
              <button
                type="button"
                onClick={() => handleAlignmentChange('center')}
                className={`alignment-button ${node.attrs.alignment === 'center' ? 'active' : ''}`}
                style={{
                  padding: '2px 4px',
                  background: node.attrs.alignment === 'center' ? 'var(--primary)' : 'transparent',
                  color: node.attrs.alignment === 'center' ? 'white' : 'inherit',
                  border: 'none',
                  borderRadius: '2px',
                  cursor: 'pointer',
                }}
              >
                C
              </button>
              <button
                type="button"
                onClick={() => handleAlignmentChange('right')}
                className={`alignment-button ${node.attrs.alignment === 'right' ? 'active' : ''}`}
                style={{
                  padding: '2px 4px',
                  background: node.attrs.alignment === 'right' ? 'var(--primary)' : 'transparent',
                  color: node.attrs.alignment === 'right' ? 'white' : 'inherit',
                  border: 'none',
                  borderRadius: '2px',
                  cursor: 'pointer',
                }}
              >
                R
              </button>
            </div>

            {/* Size options */}
            <div
              className="size-options"
              style={{
                position: 'absolute',
                bottom: '-30px',
                left: '50%',
                transform: 'translateX(-50%)',
                display: 'flex',
                gap: '4px',
                background: 'var(--background)',
                border: '1px solid var(--border)',
                borderRadius: '4px',
                padding: '2px',
                zIndex: 10,
              }}
            >
              {(['ES', 'S', 'M', 'L', 'XL', 'XXL'] as ImageSizeOption[]).map((option) => (
                <button
                  key={option}
                  type="button"
                  onClick={() => applySizeOption(option)}
                  className={`size-button ${sizeOption === option ? 'active' : ''}`}
                  style={{
                    padding: '2px 4px',
                    background: sizeOption === option ? 'var(--primary)' : 'transparent',
                    color: sizeOption === option ? 'white' : 'inherit',
                    border: 'none',
                    borderRadius: '2px',
                    cursor: 'pointer',
                    fontSize: '10px',
                    fontWeight: 'bold',
                  }}
                >
                  {option}
                </button>
              ))}
            </div>
          </>
        )}
      </div>
    </NodeViewWrapper>
  );
};
