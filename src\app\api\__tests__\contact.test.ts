import { NextRequest } from 'next/server';
import { POST } from '../contact/route';

// Mock dependencies
jest.mock('@/lib/db', () => ({
  __esModule: true,
  default: jest.fn(() => Promise.resolve()),
}));

jest.mock('@/models/Contact', () => ({
  __esModule: true,
  default: {
    create: jest.fn(),
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
  },
}));

jest.mock('@/lib/rateLimiter', () => ({
  rateLimit: jest.fn(() => Promise.resolve({ success: true })),
  getRateLimitConfig: jest.fn(() => ({ requests: 5, window: 3600 })),
}));

jest.mock('@/lib/csrf', () => ({
  verifyCSRFToken: jest.fn(() => Promise.resolve(true)),
}));

const createMockRequest = (method: string, body?: any, headers: Record<string, string> = {}) => {
  return {
    method,
    json: jest.fn(() => Promise.resolve(body)),
    headers: new Map(Object.entries(headers)),
    nextUrl: { pathname: '/api/contact' },
  } as unknown as NextRequest;
};

describe('/api/contact', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/contact', () => {
    it('should create a new contact submission', async () => {
      const mockContactData = {
        name: 'John Doe',
        email: '<EMAIL>',
        subject: 'Test Subject',
        message: 'Test message',
        category: 'general',
      };

      const mockContact = {
        _id: 'contact123',
        ...mockContactData,
        status: 'new',
        priority: 'medium',
        createdAt: new Date(),
      };

      const Contact = require('@/models/Contact').default;
      Contact.create.mockResolvedValue(mockContact);

      const request = createMockRequest('POST', mockContactData);
      const response = await POST(request);

      expect(response.status).toBe(201);
      
      const responseData = await response.json();
      expect(responseData).toHaveProperty('success', true);
      expect(responseData).toHaveProperty('message', 'Contact form submitted successfully');
      expect(responseData).toHaveProperty('contactId', 'contact123');
    });

    it('should validate required fields', async () => {
      const invalidContactData = {
        name: '',
        email: 'invalid-email',
        subject: '',
        message: '',
      };

      const request = createMockRequest('POST', invalidContactData);
      const response = await POST(request);

      expect(response.status).toBe(400);
      
      const responseData = await response.json();
      expect(responseData).toHaveProperty('success', false);
      expect(responseData).toHaveProperty('error');
    });

    it('should handle rate limiting', async () => {
      const { rateLimit } = require('@/lib/rateLimiter');
      rateLimit.mockResolvedValue({
        success: false,
        limit: 5,
        remaining: 0,
        reset: Date.now() + 3600000,
        retryAfter: 3600,
      });

      const mockContactData = {
        name: 'John Doe',
        email: '<EMAIL>',
        subject: 'Test Subject',
        message: 'Test message',
        category: 'general',
      };

      const request = createMockRequest('POST', mockContactData);
      const response = await POST(request);

      expect(response.status).toBe(429);
    });

    it('should handle CSRF validation failure', async () => {
      const { verifyCSRFToken } = require('@/lib/csrf');
      verifyCSRFToken.mockResolvedValue(false);

      const mockContactData = {
        name: 'John Doe',
        email: '<EMAIL>',
        subject: 'Test Subject',
        message: 'Test message',
        category: 'general',
      };

      const request = createMockRequest('POST', mockContactData, {
        'x-user-id': 'user123',
      });
      const response = await POST(request);

      expect(response.status).toBe(403);
    });

    it('should handle database errors', async () => {
      const Contact = require('@/models/Contact').default;
      Contact.create.mockRejectedValue(new Error('Database error'));

      const mockContactData = {
        name: 'John Doe',
        email: '<EMAIL>',
        subject: 'Test Subject',
        message: 'Test message',
        category: 'general',
      };

      const request = createMockRequest('POST', mockContactData);
      const response = await POST(request);

      expect(response.status).toBe(500);
      
      const responseData = await response.json();
      expect(responseData).toHaveProperty('success', false);
      expect(responseData).toHaveProperty('error', 'Internal server error');
    });

    it('should sanitize input data', async () => {
      const maliciousContactData = {
        name: '<script>alert("xss")</script>John Doe',
        email: '<EMAIL>',
        subject: 'Test Subject',
        message: '<img src="x" onerror="alert(1)">Test message',
        category: 'general',
      };

      const Contact = require('@/models/Contact').default;
      Contact.create.mockImplementation((data) => {
        // Verify that the data is sanitized
        expect(data.name).not.toContain('<script>');
        expect(data.message).not.toContain('<img');
        return Promise.resolve({
          _id: 'contact123',
          ...data,
          status: 'new',
          priority: 'medium',
          createdAt: new Date(),
        });
      });

      const request = createMockRequest('POST', maliciousContactData);
      const response = await POST(request);

      expect(response.status).toBe(201);
    });

    it('should set appropriate response headers', async () => {
      const mockContactData = {
        name: 'John Doe',
        email: '<EMAIL>',
        subject: 'Test Subject',
        message: 'Test message',
        category: 'general',
      };

      const Contact = require('@/models/Contact').default;
      Contact.create.mockResolvedValue({
        _id: 'contact123',
        ...mockContactData,
        status: 'new',
        priority: 'medium',
        createdAt: new Date(),
      });

      const request = createMockRequest('POST', mockContactData);
      const response = await POST(request);

      expect(response.headers.get('Content-Type')).toBe('application/json');
      expect(response.headers.get('Cache-Control')).toBe('no-store');
    });
  });
});
