#!/bin/bash

# =============================================================================
# TOOLRAPTER - DEPLOYMENT VALIDATION SCRIPT
# =============================================================================
# Comprehensive validation script for production deployment
# Usage: ./scripts/validate-deployment.sh [local|production]

set -e

# Configuration
ENVIRONMENT=${1:-local}
DOMAIN="toolrapter.com"
LOCAL_URL="http://localhost:3000"
PROD_URL="https://$DOMAIN"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Helper functions
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Determine base URL
if [ "$ENVIRONMENT" = "production" ]; then
    BASE_URL="$PROD_URL"
else
    BASE_URL="$LOCAL_URL"
fi

echo "🚀 ToolRapter Deployment Validation"
echo "Environment: $ENVIRONMENT"
echo "Base URL: $BASE_URL"
echo "=================================="

# Test 1: Basic connectivity
log_info "Testing basic connectivity..."
if curl -s --max-time 10 "$BASE_URL" > /dev/null; then
    log_success "Basic connectivity test passed"
else
    log_error "Basic connectivity test failed"
    exit 1
fi

# Test 2: Health check endpoint
log_info "Testing health check endpoint..."
HEALTH_RESPONSE=$(curl -s --max-time 5 "$BASE_URL/api/health" || echo "FAILED")
if [[ "$HEALTH_RESPONSE" == *"ok"* ]]; then
    log_success "Health check endpoint working"
else
    log_error "Health check endpoint failed"
    exit 1
fi

# Test 3: Security headers (production only)
if [ "$ENVIRONMENT" = "production" ]; then
    log_info "Testing security headers..."
    HEADERS=$(curl -s -I "$BASE_URL" | head -20)
    
    if echo "$HEADERS" | grep -q "Strict-Transport-Security"; then
        log_success "HSTS header present"
    else
        log_warning "HSTS header missing"
    fi
    
    if echo "$HEADERS" | grep -q "X-Content-Type-Options"; then
        log_success "X-Content-Type-Options header present"
    else
        log_warning "X-Content-Type-Options header missing"
    fi
    
    if echo "$HEADERS" | grep -q "X-Frame-Options"; then
        log_success "X-Frame-Options header present"
    else
        log_warning "X-Frame-Options header missing"
    fi
fi

# Test 4: API endpoints
log_info "Testing API endpoints..."

# Test blog API
BLOG_RESPONSE=$(curl -s --max-time 10 "$BASE_URL/api/blog?limit=1" || echo "FAILED")
if [[ "$BLOG_RESPONSE" == *"posts"* ]] || [[ "$BLOG_RESPONSE" == *"[]"* ]]; then
    log_success "Blog API working"
else
    log_warning "Blog API may have issues"
fi

# Test 5: Static assets
log_info "Testing static assets..."
if curl -s --max-time 5 "$BASE_URL/favicon.ico" > /dev/null; then
    log_success "Static assets accessible"
else
    log_warning "Static assets may have issues"
fi

# Test 6: Rate limiting
log_info "Testing rate limiting..."
RATE_LIMIT_COUNT=0
for i in {1..6}; do
    RESPONSE=$(curl -s -w "%{http_code}" -o /dev/null "$BASE_URL/api/health")
    if [ "$RESPONSE" = "200" ]; then
        ((RATE_LIMIT_COUNT++))
    fi
done

if [ $RATE_LIMIT_COUNT -gt 0 ]; then
    log_success "Rate limiting configured (allowed $RATE_LIMIT_COUNT/6 requests)"
else
    log_warning "Rate limiting may be too strict"
fi

# Test 7: Performance check
log_info "Testing page load performance..."
START_TIME=$(date +%s%N)
curl -s --max-time 10 "$BASE_URL" > /dev/null
END_TIME=$(date +%s%N)
LOAD_TIME=$(( (END_TIME - START_TIME) / 1000000 ))

if [ $LOAD_TIME -lt 5000 ]; then
    log_success "Page load time: ${LOAD_TIME}ms (< 5000ms target)"
else
    log_warning "Page load time: ${LOAD_TIME}ms (> 5000ms target)"
fi

# Test 8: SSL certificate (production only)
if [ "$ENVIRONMENT" = "production" ]; then
    log_info "Testing SSL certificate..."
    SSL_INFO=$(echo | openssl s_client -servername "$DOMAIN" -connect "$DOMAIN:443" 2>/dev/null | openssl x509 -noout -dates 2>/dev/null || echo "FAILED")
    
    if [[ "$SSL_INFO" != "FAILED" ]]; then
        log_success "SSL certificate valid"
        echo "$SSL_INFO" | grep "notAfter" | sed 's/notAfter=/Certificate expires: /'
    else
        log_error "SSL certificate validation failed"
    fi
fi

# Test 9: Database connectivity
log_info "Testing database connectivity..."
DB_RESPONSE=$(curl -s --max-time 10 "$BASE_URL/api/health" || echo "FAILED")
if [[ "$DB_RESPONSE" == *"ok"* ]]; then
    log_success "Database connectivity working"
else
    log_error "Database connectivity failed"
fi

# Test 10: Mobile responsiveness check
log_info "Testing mobile responsiveness..."
MOBILE_RESPONSE=$(curl -s --max-time 10 -H "User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)" "$BASE_URL" || echo "FAILED")
if [[ "$MOBILE_RESPONSE" == *"viewport"* ]]; then
    log_success "Mobile responsiveness configured"
else
    log_warning "Mobile responsiveness may have issues"
fi

echo "=================================="
log_success "Deployment validation completed!"

# Summary
echo ""
echo "📊 Validation Summary:"
echo "- Environment: $ENVIRONMENT"
echo "- Base URL: $BASE_URL"
echo "- Timestamp: $(date)"

if [ "$ENVIRONMENT" = "production" ]; then
    echo ""
    echo "🔗 Quick Links:"
    echo "- Website: $PROD_URL"
    echo "- Admin: $PROD_URL/admin"
    echo "- Health: $PROD_URL/api/health"
fi
