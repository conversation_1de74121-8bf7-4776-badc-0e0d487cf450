import mongoose, { Schema, Document, Types } from "mongoose";

export interface IContactReply {
  subject: string;
  message: string;
  sentBy: Types.ObjectId;
  sentAt: Date;
  emailId?: string;
}

export interface IContact extends Document {
  _id: Types.ObjectId;
  name: string;
  email: string;
  category: "general" | "technical" | "bug" | "feature" | "business";
  message: string;
  status: "new" | "read" | "in-progress" | "resolved";
  priority: "low" | "medium" | "high";
  assignedTo?: Types.ObjectId;
  internalNotes?: string;
  responseSent: boolean;
  replies?: IContactReply[];
  ipAddress?: string;
  userAgent?: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
}

const contactSchema = new Schema<IContact>(
  {
    name: {
      type: String,
      required: [true, "Name is required"],
      trim: true,
      minlength: [2, "Name must be at least 2 characters"],
      maxlength: [100, "Name must be less than 100 characters"],
    },
    email: {
      type: String,
      required: [true, "Email is required"],
      trim: true,
      lowercase: true,
      match: [
        /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
        "Please provide a valid email",
      ],
      index: true,
    },
    category: {
      type: String,
      required: [true, "Category is required"],
      enum: {
        values: ["general", "technical", "bug", "feature", "business"],
        message: "Category must be one of: general, technical, bug, feature, business",
      },
      index: true,
    },
    message: {
      type: String,
      required: [true, "Message is required"],
      trim: true,
      minlength: [10, "Message must be at least 10 characters"],
      maxlength: [5000, "Message must be less than 5000 characters"],
    },
    status: {
      type: String,
      enum: {
        values: ["new", "read", "in-progress", "resolved"],
        message: "Status must be one of: new, read, in-progress, resolved",
      },
      default: "new",
      index: true,
    },
    priority: {
      type: String,
      enum: {
        values: ["low", "medium", "high"],
        message: "Priority must be one of: low, medium, high",
      },
      default: "medium",
      index: true,
    },
    assignedTo: {
      type: Schema.Types.ObjectId,
      ref: "User",
      index: true,
    },
    internalNotes: {
      type: String,
      trim: true,
      maxlength: [2000, "Internal notes must be less than 2000 characters"],
    },
    responseSent: {
      type: Boolean,
      default: false,
      index: true,
    },
    replies: [{
      subject: {
        type: String,
        required: true,
        trim: true,
        maxlength: [200, "Reply subject must be less than 200 characters"],
      },
      message: {
        type: String,
        required: true,
        trim: true,
        maxlength: [5000, "Reply message must be less than 5000 characters"],
      },
      sentBy: {
        type: Schema.Types.ObjectId,
        ref: "User",
        required: true,
      },
      sentAt: {
        type: Date,
        default: Date.now,
      },
      emailId: {
        type: String,
        trim: true,
      },
    }],
    ipAddress: {
      type: String,
      trim: true,
    },
    userAgent: {
      type: String,
      trim: true,
    },
    deletedAt: {
      type: Date,
      index: true,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Create compound indexes for efficient queries
contactSchema.index({ status: 1, createdAt: -1 });
contactSchema.index({ category: 1, status: 1 });
contactSchema.index({ assignedTo: 1, status: 1 });
contactSchema.index({ createdAt: -1 });
contactSchema.index({ deletedAt: 1, status: 1 });

// Text index for search functionality
contactSchema.index(
  { 
    name: "text", 
    email: "text", 
    message: "text",
    internalNotes: "text"
  },
  { 
    weights: { 
      name: 10, 
      email: 8, 
      message: 5, 
      internalNotes: 3 
    },
    name: "contact_text_index"
  }
);

// Virtual for assigned user details
contactSchema.virtual("assignedUser", {
  ref: "User",
  localField: "assignedTo",
  foreignField: "_id",
  justOne: true,
});

// Method to check if contact is deleted
contactSchema.methods.isDeleted = function() {
  return !!this.deletedAt;
};

// Method to soft delete
contactSchema.methods.softDelete = function() {
  this.deletedAt = new Date();
  return this.save();
};

// Method to restore
contactSchema.methods.restore = function() {
  this.deletedAt = undefined;
  return this.save();
};

// Static method to find non-deleted contacts
contactSchema.statics.findActive = function(filter = {}) {
  return this.find({ ...filter, deletedAt: { $exists: false } });
};

// Static method to get contact statistics
contactSchema.statics.getStats = async function() {
  const stats = await this.aggregate([
    { $match: { deletedAt: { $exists: false } } },
    {
      $group: {
        _id: null,
        total: { $sum: 1 },
        new: { $sum: { $cond: [{ $eq: ["$status", "new"] }, 1, 0] } },
        read: { $sum: { $cond: [{ $eq: ["$status", "read"] }, 1, 0] } },
        inProgress: { $sum: { $cond: [{ $eq: ["$status", "in-progress"] }, 1, 0] } },
        resolved: { $sum: { $cond: [{ $eq: ["$status", "resolved"] }, 1, 0] } },
        highPriority: { $sum: { $cond: [{ $eq: ["$priority", "high"] }, 1, 0] } },
        responseSent: { $sum: { $cond: ["$responseSent", 1, 0] } },
      },
    },
  ]);

  return stats[0] || {
    total: 0,
    new: 0,
    read: 0,
    inProgress: 0,
    resolved: 0,
    highPriority: 0,
    responseSent: 0,
  };
};

// Pre-save middleware to update timestamps
contactSchema.pre("save", function(next) {
  if (this.isModified("status") && this.status === "read" && this.isNew === false) {
    // Auto-update when status changes to read
  }
  next();
});

// Prevent mongoose from creating the model multiple times during hot reloads
const Contact = mongoose.models.Contact || mongoose.model<IContact>("Contact", contactSchema);

export default Contact;
