// scripts/seed-admin.js
const { MongoClient } = require("mongodb");
const bcrypt = require("bcryptjs");
require("dotenv").config({ path: ".env.local" });

const MONGODB_URI = process.env.MONGODB_URI;

if (!MONGODB_URI) {
  console.error("MONGODB_URI environment variable is not set");
  process.exit(1);
}

async function seedAdmin() {
  const client = new MongoClient(MONGODB_URI);

  try {
    await client.connect();
    console.log("Connected to MongoDB");

    const db = client.db();
    const users = db.collection("users");

    // Check if admin already exists
    const existingAdmin = await users.findOne({ email: "<EMAIL>" });

    if (existingAdmin) {
      console.log("Admin user already exists, updating password...");
      // Update the password to ensure it's correct
      const hashedPassword = await bcrypt.hash("admin123", 12);
      await users.updateOne(
        { email: "<EMAIL>" },
        {
          $set: {
            password: hashedPassword,
            updatedAt: new Date()
          }
        }
      );
      console.log("Admin password updated successfully");
      console.log("Login credentials:");
      console.log("Email: <EMAIL>");
      console.log("Password: admin123");
      return;
    }

    // Create admin user
    const hashedPassword = await bcrypt.hash("admin123", 12);

    const adminUser = {
      name: "Admin User",
      email: "<EMAIL>",
      password: hashedPassword,
      role: "admin",
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const result = await users.insertOne(adminUser);
    console.log("Admin user created successfully:", result.insertedId);
    console.log("Login credentials:");
    console.log("Email: <EMAIL>");
    console.log("Password: admin123");

  } catch (error) {
    console.error("Error seeding admin user:", error);
  } finally {
    await client.close();
  }
}

seedAdmin();
