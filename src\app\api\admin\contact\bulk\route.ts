import { NextRequest, NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import { z } from "zod";
import connectToDatabase from "@/lib/db";
import Contact from "@/models/Contact";
import mongoose from "mongoose";

const secret = process.env.NEXTAUTH_SECRET;

// Validation schema for bulk operations
const BulkOperationSchema = z.object({
  action: z.enum(["updateStatus", "updatePriority", "assign", "delete", "markResponseSent"]),
  contactIds: z.array(z.string()).min(1, "At least one contact ID is required"),
  data: z.object({
    status: z.enum(["new", "read", "in-progress", "resolved"]).optional(),
    priority: z.enum(["low", "medium", "high"]).optional(),
    assignedTo: z.string().optional().nullable(),
    responseSent: z.boolean().optional(),
  }).optional(),
});

// Helper function to check admin authorization
async function checkAdminAuth(request: NextRequest) {
  const token = await getToken({ req: request, secret });
  
  if (!token || token.role !== "admin") {
    return NextResponse.json(
      { error: "Unauthorized. Admin access required." },
      { status: 401 }
    );
  }
  
  return null;
}

// Helper function to validate ObjectIds
function validateObjectIds(ids: string[]): boolean {
  return ids.every(id => mongoose.Types.ObjectId.isValid(id));
}

// POST /api/admin/contact/bulk - Perform bulk operations on contacts
export async function POST(request: NextRequest) {
  try {
    // Check admin authorization
    const authError = await checkAdminAuth(request);
    if (authError) return authError;

    await connectToDatabase();

    const body = await request.json();
    const validation = BulkOperationSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: "Validation failed", details: validation.error.issues },
        { status: 400 }
      );
    }

    const { action, contactIds, data } = validation.data;

    // Validate all contact IDs
    if (!validateObjectIds(contactIds)) {
      return NextResponse.json(
        { error: "One or more invalid contact IDs" },
        { status: 400 }
      );
    }

    // Build the filter for non-deleted contacts
    const filter = {
      _id: { $in: contactIds },
      deletedAt: { $exists: false }
    };

    let updateData: any = {};
    let result;

    switch (action) {
      case "updateStatus":
        if (!data?.status) {
          return NextResponse.json(
            { error: "Status is required for updateStatus action" },
            { status: 400 }
          );
        }
        updateData = { status: data.status };
        break;

      case "updatePriority":
        if (!data?.priority) {
          return NextResponse.json(
            { error: "Priority is required for updatePriority action" },
            { status: 400 }
          );
        }
        updateData = { priority: data.priority };
        break;

      case "assign":
        if (data?.assignedTo === undefined) {
          return NextResponse.json(
            { error: "AssignedTo is required for assign action" },
            { status: 400 }
          );
        }
        
        // Handle unassign (null/empty string)
        if (!data.assignedTo || data.assignedTo === "") {
          updateData = { $unset: { assignedTo: "" } };
        } else {
          // Validate assignedTo ObjectId
          if (!mongoose.Types.ObjectId.isValid(data.assignedTo)) {
            return NextResponse.json(
              { error: "Invalid assignedTo user ID" },
              { status: 400 }
            );
          }
          updateData = { assignedTo: data.assignedTo };
        }
        break;

      case "markResponseSent":
        if (data?.responseSent === undefined) {
          return NextResponse.json(
            { error: "ResponseSent is required for markResponseSent action" },
            { status: 400 }
          );
        }
        updateData = { responseSent: data.responseSent };
        break;

      case "delete":
        updateData = { deletedAt: new Date() };
        break;

      default:
        return NextResponse.json(
          { error: "Invalid action" },
          { status: 400 }
        );
    }

    // Perform the bulk update
    if (action === "assign" && updateData.$unset) {
      result = await Contact.updateMany(filter, updateData);
    } else {
      result = await Contact.updateMany(filter, updateData);
    }

    // Get updated contacts for response
    const updatedContacts = await Contact.find({
      _id: { $in: contactIds },
      deletedAt: { $exists: false }
    }).populate("assignedTo", "name email");

    return NextResponse.json({
      success: true,
      data: {
        modifiedCount: result.modifiedCount,
        matchedCount: result.matchedCount,
        contacts: updatedContacts,
      },
      message: `Bulk ${action} completed successfully. ${result.modifiedCount} contacts updated.`,
    });

  } catch (error) {
    console.error("Error performing bulk operation:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// GET /api/admin/contact/bulk/export - Export contacts as CSV
export async function GET(request: NextRequest) {
  try {
    // Check admin authorization
    const authError = await checkAdminAuth(request);
    if (authError) return authError;

    await connectToDatabase();

    // Parse query parameters for filtering
    const { searchParams } = new URL(request.url);
    const status = searchParams.get("status");
    const category = searchParams.get("category");
    const priority = searchParams.get("priority");
    const dateRange = searchParams.get("dateRange");

    // Build filter
    const filter: any = { deletedAt: { $exists: false } };

    if (status && status !== "all") {
      filter.status = status;
    }

    if (category && category !== "all") {
      filter.category = category;
    }

    if (priority && priority !== "all") {
      filter.priority = priority;
    }

    if (dateRange && dateRange !== "all") {
      const days = parseInt(dateRange);
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);
      filter.createdAt = { $gte: startDate };
    }

    // Fetch contacts
    const contacts = await Contact.find(filter)
      .populate("assignedTo", "name email")
      .sort({ createdAt: -1 })
      .lean();

    // Convert to CSV format
    const csvHeaders = [
      "ID",
      "Name",
      "Email",
      "Category",
      "Status",
      "Priority",
      "Assigned To",
      "Response Sent",
      "Created At",
      "Updated At",
      "Message"
    ];

    const csvRows = contacts.map((contact: any) => [
      contact._id.toString(),
      contact.name,
      contact.email,
      contact.category,
      contact.status,
      contact.priority,
      contact.assignedTo ? `${contact.assignedTo.name} (${contact.assignedTo.email})` : "",
      contact.responseSent ? "Yes" : "No",
      new Date(contact.createdAt).toISOString(),
      new Date(contact.updatedAt).toISOString(),
      `"${contact.message.replace(/"/g, '""')}"` // Escape quotes in message
    ]);

    const csvContent = [
      csvHeaders.join(","),
      ...csvRows.map(row => row.join(","))
    ].join("\n");

    // Return CSV response
    return new NextResponse(csvContent, {
      status: 200,
      headers: {
        "Content-Type": "text/csv",
        "Content-Disposition": `attachment; filename="contacts-export-${new Date().toISOString().split('T')[0]}.csv"`,
      },
    });

  } catch (error) {
    console.error("Error exporting contacts:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
