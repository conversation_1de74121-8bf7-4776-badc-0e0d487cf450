/**
 * Haptic Feedback Utilities
 * 
 * Provides haptic feedback for touch interactions on supported devices.
 * Falls back gracefully on devices that don't support haptic feedback.
 */

// Check if haptic feedback is supported
const isHapticSupported = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  // Check for Vibration API
  return 'vibrate' in navigator || 'webkitVibrate' in navigator;
};

// Check if device supports advanced haptic feedback (iOS Safari)
const isAdvancedHapticSupported = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  // Check for iOS Safari haptic feedback
  return 'ontouchstart' in window && /iPad|iPhone|iPod/.test(navigator.userAgent);
};

// Vibration patterns for different feedback types
const VIBRATION_PATTERNS = {
  success: [50],           // Short single vibration
  warning: [100, 50, 100], // Double vibration
  error: [200],            // Long vibration
  impact: [25],            // Very short vibration
  selection: [10],         // Minimal vibration
  longPress: [50, 100, 50], // Pattern for long press
} as const;

/**
 * Trigger success haptic feedback
 * Used for successful actions like form submissions, successful operations
 */
export const success = (): void => {
  try {
    if (isAdvancedHapticSupported()) {
      // iOS Safari - use selection change feedback
      const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
      audio.volume = 0;
      audio.play().catch(() => {});
    } else if (isHapticSupported()) {
      navigator.vibrate(VIBRATION_PATTERNS.success);
    }
  } catch (error) {
    // Silently fail - haptic feedback is not critical
  }
};

/**
 * Trigger warning haptic feedback
 * Used for warnings, validation errors, or cautionary actions
 */
export const warning = (): void => {
  try {
    if (isHapticSupported()) {
      navigator.vibrate(VIBRATION_PATTERNS.warning);
    }
  } catch (error) {
    // Silently fail
  }
};

/**
 * Trigger error haptic feedback
 * Used for errors, failed operations, or destructive actions
 */
export const error = (): void => {
  try {
    if (isHapticSupported()) {
      navigator.vibrate(VIBRATION_PATTERNS.error);
    }
  } catch (error) {
    // Silently fail
  }
};

/**
 * Trigger light impact haptic feedback
 * Used for button taps, selections, and general interactions
 */
export const impact = (): void => {
  try {
    if (isHapticSupported()) {
      navigator.vibrate(VIBRATION_PATTERNS.impact);
    }
  } catch (error) {
    // Silently fail
  }
};

/**
 * Trigger selection haptic feedback
 * Used for selection changes, toggles, and switches
 */
export const selection = (): void => {
  try {
    if (isHapticSupported()) {
      navigator.vibrate(VIBRATION_PATTERNS.selection);
    }
  } catch (error) {
    // Silently fail
  }
};

/**
 * Trigger long press haptic feedback
 * Used for long press interactions and context menus
 */
export const longPress = (): void => {
  try {
    if (isHapticSupported()) {
      navigator.vibrate(VIBRATION_PATTERNS.longPress);
    }
  } catch (error) {
    // Silently fail
  }
};

/**
 * Custom haptic feedback with specific pattern
 * @param pattern - Vibration pattern (array of durations in milliseconds)
 */
export const custom = (pattern: number[]): void => {
  try {
    if (isHapticSupported()) {
      navigator.vibrate(pattern);
    }
  } catch (error) {
    // Silently fail
  }
};

/**
 * Stop all haptic feedback
 */
export const stop = (): void => {
  try {
    if (isHapticSupported()) {
      navigator.vibrate(0);
    }
  } catch (error) {
    // Silently fail
  }
};

/**
 * Check if haptic feedback is available
 */
export const isAvailable = (): boolean => {
  return isHapticSupported();
};

/**
 * Get device haptic capabilities
 */
export const getCapabilities = () => {
  return {
    basic: isHapticSupported(),
    advanced: isAdvancedHapticSupported(),
    patterns: isHapticSupported(),
  };
};

// Default export with all haptic functions
export default {
  success,
  warning,
  error,
  impact,
  selection,
  longPress,
  custom,
  stop,
  isAvailable,
  getCapabilities,
};
