# 🚀 Deployment Guide

## 🎯 Overview

This guide provides comprehensive instructions for deploying ToolCrush to various hosting platforms with enterprise-grade configurations, security, and performance optimizations.

## 🌐 Deployment Options

### **1. Vercel (Recommended for Serverless)**
- ✅ Zero-configuration deployment
- ✅ Automatic HTTPS and CDN
- ✅ Built-in analytics and monitoring
- ✅ Edge functions and middleware support
- ✅ Automatic scaling

### **2. <PERSON>inger VPS (Recommended for Full Control)**
- ✅ Complete server control
- ✅ Custom configurations
- ✅ Cost-effective for high traffic
- ✅ PM2 process management
- ✅ Nginx reverse proxy

### **3. AWS/DigitalOcean (Enterprise)**
- ✅ Maximum scalability
- ✅ Advanced monitoring
- ✅ Multi-region deployment
- ✅ Load balancing
- ✅ Auto-scaling groups

## 🔧 Pre-Deployment Checklist

### **Environment Setup**
- [ ] Environment variables configured
- [ ] Database connection tested
- [ ] Redis instance configured
- [ ] OAuth credentials verified
- [ ] Email service configured
- [ ] Domain and SSL certificates ready

### **Code Preparation**
- [ ] All tests passing
- [ ] Build successful locally
- [ ] Dependencies updated
- [ ] Security scan completed
- [ ] Performance benchmarks met
- [ ] Documentation updated

## 🌟 Vercel Deployment

### **Quick Deploy**
```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy
vercel

# Set environment variables
vercel env add NEXTAUTH_SECRET
vercel env add MONGODB_URI
vercel env add UPSTASH_REDIS_REST_URL
# ... add all required variables

# Deploy to production
vercel --prod
```

### **Vercel Configuration**
```json
{
  "version": 2,
  "name": "toolcrush",
  "framework": "nextjs",
  "regions": ["iad1", "sfo1"],
  "functions": {
    "src/app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        }
      ]
    }
  ]
}
```

### **Environment Variables (Vercel)**
```bash
# Required variables
NEXTAUTH_SECRET=your-secret-key
NEXTAUTH_URL=https://toolcrush.com
MONGODB_URI=mongodb+srv://...
UPSTASH_REDIS_REST_URL=https://...
UPSTASH_REDIS_REST_TOKEN=...
GOOGLE_CLIENT_ID=...
GOOGLE_CLIENT_SECRET=...

# Optional variables
CLOUDINARY_CLOUD_NAME=...
SMTP_HOST=smtp.gmail.com
SMTP_USER=...
SMTP_PASS=...
```

## 🖥️ Hostinger VPS Deployment

### **Server Setup**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2
sudo npm install -g pm2

# Install Nginx
sudo apt install nginx -y

# Install Certbot for SSL
sudo apt install certbot python3-certbot-nginx -y
```

### **Application Deployment**
```bash
# Clone repository
cd /var/www
sudo git clone https://github.com/MuhammadShahbaz195/ToolCrush.git toolcrush
cd toolcrush

# Set permissions
sudo chown -R $USER:$USER /var/www/toolcrush

# Install dependencies
npm ci --production

# Download fonts
npm run download-fonts

# Build application
npm run build

# Start with PM2
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup
```

### **Nginx Configuration**
```bash
# Copy Nginx configuration
sudo cp nginx.conf /etc/nginx/sites-available/toolcrush

# Enable site
sudo ln -s /etc/nginx/sites-available/toolcrush /etc/nginx/sites-enabled/

# Test configuration
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
```

### **SSL Certificate Setup**
```bash
# Obtain SSL certificate
sudo certbot --nginx -d toolcrush.com -d www.toolcrush.com

# Test auto-renewal
sudo certbot renew --dry-run

# Set up auto-renewal cron job
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -
```

## 🔄 CI/CD Pipeline

### **GitHub Actions Setup**
1. **Repository Secrets**
   ```
   # VPS deployment secrets
   VPS_SSH_KEY=your-private-ssh-key
   MONGODB_URI=your-mongodb-connection-string
   NEXTAUTH_SECRET=your-nextauth-secret
   EMAIL_SERVER_PASSWORD=your-email-password
   UPSTASH_REDIS_REST_TOKEN=your-redis-token
   ```

2. **Workflow Triggers**
   - Push to `main` → Production VPS deployment
   - Manual trigger → Staging VPS deployment
   - Pull requests → Automated testing and security scanning

### **Automated Deployment Process**
```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline
on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
      - run: npm ci
      - run: npm test
      - run: npm run build
```

## 🔧 Environment-Specific Configurations

### **Development**
```env
NODE_ENV=development
NEXT_PUBLIC_BASE_URL=http://localhost:3000
NEXTAUTH_URL=http://localhost:3000
# Use local MongoDB or development cluster
```

### **Staging**
```env
NODE_ENV=production
NEXT_PUBLIC_BASE_URL=https://staging.toolcrush.com
NEXTAUTH_URL=https://staging.toolcrush.com
# Use staging database and services
```

### **Production**
```env
NODE_ENV=production
NEXT_PUBLIC_BASE_URL=https://toolcrush.com
NEXTAUTH_URL=https://toolcrush.com
# Use production database and services
ENABLE_STRICT_CSP=true
ENABLE_SECURITY_HEADERS=true
```

## 📊 Monitoring & Logging

### **Application Monitoring**
```bash
# PM2 monitoring
pm2 monit

# View logs
pm2 logs toolcrush

# Application metrics
pm2 show toolcrush
```

### **Server Monitoring**
```bash
# System resources
htop
df -h
free -m

# Nginx logs
sudo tail -f /var/log/nginx/toolcrush_access.log
sudo tail -f /var/log/nginx/toolcrush_error.log

# Application logs
tail -f /var/log/pm2/toolcrush.log
```

### **Performance Monitoring**
- **Vercel Analytics**: Built-in performance monitoring
- **Uptime Robot**: Uptime monitoring
- **Google Analytics**: User behavior tracking
- **Sentry**: Error tracking and performance monitoring

## 🔄 Backup & Recovery

### **Database Backup**
```bash
# MongoDB backup
mongodump --uri="mongodb+srv://..." --out=/backup/$(date +%Y%m%d)

# Automated backup script
#!/bin/bash
BACKUP_DIR="/backup/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR
mongodump --uri="$MONGODB_URI" --out=$BACKUP_DIR
tar -czf "$BACKUP_DIR.tar.gz" $BACKUP_DIR
rm -rf $BACKUP_DIR
```

### **Application Backup**
```bash
# Create application backup
tar -czf /backup/app-$(date +%Y%m%d).tar.gz /var/www/toolcrush

# Backup environment variables
cp .env.local /backup/env-$(date +%Y%m%d).backup
```

### **Recovery Procedures**
```bash
# Restore from backup
cd /var/www
sudo tar -xzf /backup/app-20240101.tar.gz
sudo chown -R $USER:$USER toolcrush
cd toolcrush
pm2 restart toolcrush
```

## 🚨 Troubleshooting

### **Common Issues**

**Build Failures**
```bash
# Clear cache and rebuild
rm -rf .next node_modules
npm ci
npm run build
```

**PM2 Issues**
```bash
# Restart PM2
pm2 restart toolcrush
pm2 logs toolcrush

# Reset PM2
pm2 kill
pm2 start ecosystem.config.js
```

**Nginx Issues**
```bash
# Test configuration
sudo nginx -t

# Check status
sudo systemctl status nginx

# Restart Nginx
sudo systemctl restart nginx
```

**SSL Certificate Issues**
```bash
# Renew certificate
sudo certbot renew

# Check certificate status
sudo certbot certificates
```

### **Performance Issues**
```bash
# Check system resources
htop
df -h
free -m

# Monitor application
pm2 monit

# Check database performance
# Use MongoDB Compass or monitoring tools
```

## 📋 Post-Deployment Checklist

### **Verification Steps**
- [ ] Application loads successfully
- [ ] All pages render correctly
- [ ] Authentication works
- [ ] Database connections active
- [ ] API endpoints responding
- [ ] SSL certificate valid
- [ ] Performance metrics acceptable

### **Security Verification**
- [ ] Security headers present
- [ ] Rate limiting active
- [ ] CSRF protection enabled
- [ ] Input validation working
- [ ] Error handling secure
- [ ] Logs not exposing sensitive data

### **Performance Verification**
- [ ] Page load times < 5 seconds
- [ ] Lighthouse score > 90
- [ ] Core Web Vitals green
- [ ] Mobile performance optimized
- [ ] CDN caching working
- [ ] Database queries optimized

## 🔗 Deployment Resources

### **Documentation**
- [Vercel Deployment](https://vercel.com/docs/deployments)
- [Next.js Deployment](https://nextjs.org/docs/deployment)
- [PM2 Documentation](https://pm2.keymetrics.io/docs/)
- [Nginx Configuration](https://nginx.org/en/docs/)

### **Tools**
- [Vercel CLI](https://vercel.com/cli)
- [PM2](https://pm2.keymetrics.io/)
- [Certbot](https://certbot.eff.org/)
- [GitHub Actions](https://github.com/features/actions)

### **Monitoring Services**
- [Uptime Robot](https://uptimerobot.com/)
- [Pingdom](https://www.pingdom.com/)
- [New Relic](https://newrelic.com/)
- [DataDog](https://www.datadoghq.com/)
