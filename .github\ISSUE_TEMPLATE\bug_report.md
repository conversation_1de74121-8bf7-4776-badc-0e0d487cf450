---
name: Bug report
about: Create a report to help us improve
title: '[BUG] '
labels: 'bug'
assignees: ''
---

## 🐛 Bug Description
A clear and concise description of what the bug is.

## 🔄 Steps to Reproduce
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

## ✅ Expected Behavior
A clear and concise description of what you expected to happen.

## 📸 Screenshots
If applicable, add screenshots to help explain your problem.

## 💻 Environment
- **OS**: [e.g. Windows 11, macOS 13, Ubuntu 22.04]
- **Browser**: [e.g. Chrome 120, Firefox 121, Safari 17]
- **Device**: [e.g. Desktop, iPhone 15, Samsung Galaxy S23]
- **Screen Size**: [e.g. 1920x1080, Mobile]

## 📱 Mobile Specific (if applicable)
- **Device**: [e.g. iPhone 15, Samsung Galaxy S23]
- **OS Version**: [e.g. iOS 17.1, Android 14]
- **Browser**: [e.g. <PERSON><PERSON>, Chrome Mobile]
- **Touch Issues**: [e.g. buttons not responding, scroll problems]

## 🔧 Additional Context
Add any other context about the problem here.

## 🔍 Error Logs
If applicable, paste any error messages or console logs:

```
Paste error logs here
```

## 🎯 Priority
- [ ] Low - Minor issue, workaround available
- [ ] Medium - Affects functionality but not critical
- [ ] High - Blocks important functionality
- [ ] Critical - Breaks core features or security issue
