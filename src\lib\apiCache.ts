/**
 * API Cache Service
 *
 * This service provides a way to cache API responses to avoid unnecessary API calls.
 * It uses localStorage for persistence and supports TTL (time-to-live) for cache entries.
 */

import logger from '@/lib/secureLogger';

// Default cache TTL in milliseconds (5 minutes)
const DEFAULT_CACHE_TTL = 5 * 60 * 1000;

// Prefix for all cache keys to avoid collisions
const CACHE_KEY_PREFIX = 'api_cache_';

// Cache entry interface
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

/**
 * Get cached API response
 *
 * @param key - The cache key (usually the API endpoint URL)
 * @returns The cached data or null if not found or expired
 */
export function getCachedResponse<T>(key: string): T | null {
  if (typeof window === 'undefined') {
    return null;
  }

  try {
    const cacheKey = `${CACHE_KEY_PREFIX}${key}`;
    const cachedData = localStorage.getItem(cacheKey);

    if (!cachedData) {
      return null;
    }

    const cacheEntry: CacheEntry<T> = JSON.parse(cachedData);
    const now = Date.now();

    // Check if cache is expired
    if (now - cacheEntry.timestamp > cacheEntry.ttl) {
      // Remove expired cache
      localStorage.removeItem(cacheKey);
      return null;
    }

    return cacheEntry.data;
  } catch (error) {
    // Silent fail and return null on any error
    logger.error('Error reading API cache');
    return null;
  }
}

/**
 * Set API response in cache
 *
 * @param key - The cache key (usually the API endpoint URL)
 * @param data - The data to cache
 * @param ttl - Time-to-live in milliseconds (default: 5 minutes)
 */
export function setCachedResponse<T>(key: string, data: T, ttl = DEFAULT_CACHE_TTL): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    const cacheKey = `${CACHE_KEY_PREFIX}${key}`;
    const cacheEntry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl
    };

    localStorage.setItem(cacheKey, JSON.stringify(cacheEntry));

    // Log success without sensitive data
    logger.success(`API cache set for: ${key}`);
  } catch (error) {
    // Silent fail on any error
    logger.error('Error setting API cache');
  }
}

/**
 * Clear specific API cache entry
 *
 * @param key - The cache key to clear
 */
export function clearCacheEntry(key: string): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    const cacheKey = `${CACHE_KEY_PREFIX}${key}`;
    localStorage.removeItem(cacheKey);

    // Log success
    logger.success(`API cache cleared for: ${key}`);
  } catch (error) {
    // Silent fail on any error
    logger.error('Error clearing API cache');
  }
}

/**
 * Clear all API cache entries
 */
export function clearAllCache(): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    // Get all keys from localStorage
    const keys = Object.keys(localStorage);

    // Remove all keys that start with the cache prefix
    keys.forEach(key => {
      if (key.startsWith(CACHE_KEY_PREFIX)) {
        localStorage.removeItem(key);
      }
    });

    // Log success
    logger.success('All API cache cleared');
  } catch (error) {
    // Silent fail on any error
    logger.error('Error clearing all API cache');
  }
}

/**
 * Enhanced fetch function with caching
 *
 * @param url - The URL to fetch
 * @param options - Fetch options
 * @param cacheOptions - Caching options
 * @returns The response data
 */
export async function fetchWithCache<T>(
  url: string,
  options: RequestInit = {},
  cacheOptions: {
    ttl?: number;
    bypassCache?: boolean;
    cacheKey?: string;
  } = {}
): Promise<T> {
  const {
    ttl = DEFAULT_CACHE_TTL,
    bypassCache = false,
    cacheKey = url
  } = cacheOptions;

  // Try to get from cache first (unless bypassing cache)
  if (!bypassCache) {
    const cachedData = getCachedResponse<T>(cacheKey);
    if (cachedData) {
      logger.success(`Using cached data for: ${url}`);
      return cachedData;
    }
  }

  // Fetch fresh data with timeout
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
      cache: 'no-store',
      headers: {
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        ...(options.headers || {})
      }
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    // Cache the response
    setCachedResponse<T>(cacheKey, data, ttl);

    // Log success without sensitive data
    logger.success(`Fresh data fetched for: ${url}`);

    return data;
  } catch (error) {
    // If it's an abort error, throw a more user-friendly message
    if (error instanceof Error && error.name === 'AbortError') {
      throw new Error('Request timed out. Please try again.');
    }
    // Re-throw the original error
    throw error;
  }
}
