const fs = require('fs');
const path = require('path');
const https = require('https');

// Create directory if it doesn't exist
const fontDir = path.join(__dirname, '../public/fonts/inter');
if (!fs.existsSync(fontDir)) {
  fs.mkdirSync(fontDir, { recursive: true });
}

// Font files to download
const fonts = [
  {
    url: 'https://fonts.gstatic.com/s/inter/v13/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa1ZL7.woff2',
    filename: 'Inter-Regular.woff2'
  },
  {
    url: 'https://fonts.gstatic.com/s/inter/v13/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa2JL7SUc.woff2',
    filename: 'Inter-Medium.woff2'
  },
  {
    url: 'https://fonts.gstatic.com/s/inter/v13/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa25L7SUc.woff2',
    filename: 'Inter-SemiBold.woff2'
  },
  {
    url: 'https://fonts.gstatic.com/s/inter/v13/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa2pL7SUc.woff2',
    filename: 'Inter-Bold.woff2'
  }
];

// Download function
function downloadFont(url, filename) {
  return new Promise((resolve, reject) => {
    const filePath = path.join(fontDir, filename);
    const file = fs.createWriteStream(filePath);
    
    https.get(url, response => {
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`Downloaded ${filename}`);
        resolve();
      });
      
      file.on('error', err => {
        fs.unlink(filePath, () => {}); // Delete the file if there's an error
        console.error(`Error downloading ${filename}:`, err.message);
        reject(err);
      });
    }).on('error', err => {
      fs.unlink(filePath, () => {});
      console.error(`Error downloading ${filename}:`, err.message);
      reject(err);
    });
  });
}

// Download all fonts
async function downloadAllFonts() {
  console.log('Starting font downloads...');
  
  try {
    await Promise.all(fonts.map(font => downloadFont(font.url, font.filename)));
    console.log('All fonts downloaded successfully!');
  } catch (error) {
    console.error('Error downloading fonts:', error);
    process.exit(1);
  }
}

downloadAllFonts();
