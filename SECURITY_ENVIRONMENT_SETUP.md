# 🔐 Security Environment Setup Guide

## 🎯 Overview

This guide provides comprehensive setup instructions for enterprise-grade security in the ToolBox application, including Upstash Redis rate limiting, CSRF protection, CSP headers, and Edge Runtime compatibility.

## 📋 Required Environment Variables

### Core Security Variables
```env
# NextAuth.js Configuration
NEXTAUTH_SECRET=your-super-secure-secret-key-here
NEXTAUTH_URL=https://your-domain.com

# Database Configuration
MONGODB_URI=mongodb+srv://username:<EMAIL>/database

# Upstash Redis for Rate Limiting
UPSTASH_REDIS_REST_URL=https://your-redis-url.upstash.io
UPSTASH_REDIS_REST_TOKEN=your-redis-token

# OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Cloudinary Configuration (if using image uploads)
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# Security Configuration
NODE_ENV=production
NEXT_PUBLIC_BASE_URL=https://your-domain.com
```

## 🛡️ Security Implementation Status

### ✅ Implemented Features
- **Rate Limiting**: Upstash Redis with tiered limits
- **CSRF Protection**: Double-submit cookie pattern
- **Security Headers**: Comprehensive CSP and security headers
- **Authentication**: NextAuth.js with JWT tokens
- **Input Validation**: Zod schemas for all inputs
- **Error Handling**: Sanitized error responses

### ⚠️ Critical Security Fixes Needed

#### 1. Enhanced CSP Headers
Current CSP allows `unsafe-inline` and `unsafe-eval`. For maximum security:

```typescript
// Enhanced CSP for production
const STRICT_CSP = [
  "default-src 'self'",
  "script-src 'self' 'nonce-{nonce}' https://accounts.google.com",
  "style-src 'self' 'nonce-{nonce}' https://fonts.googleapis.com",
  "font-src 'self' https://fonts.gstatic.com",
  "img-src 'self' data: https: blob:",
  "connect-src 'self' https://accounts.google.com https://api.cloudinary.com",
  "object-src 'none'",
  "base-uri 'self'",
  "form-action 'self'",
  "frame-ancestors 'none'",
  "upgrade-insecure-requests"
].join('; ');
```

#### 2. Rate Limiting Configuration
Ensure Upstash Redis is properly configured:

```typescript
// Rate limit configurations
export const RATE_LIMITS = {
  general: { requests: 100, window: 15 * 60 }, // 100/15min
  contact: { requests: 5, window: 60 * 60 },   // 5/hour
  auth: { requests: 10, window: 15 * 60 },     // 10/15min
  admin: { requests: 50, window: 15 * 60 }     // 50/15min
};
```

## 🚀 Performance Requirements

### Security Overhead Targets
- **Middleware Processing**: < 50ms
- **Rate Limit Check**: < 10ms
- **CSRF Validation**: < 5ms
- **Security Headers**: < 1ms

### Monitoring
```typescript
// Performance monitoring for security middleware
const startTime = performance.now();
// ... security operations
const duration = performance.now() - startTime;
if (duration > 50) {
  console.warn(`Security middleware slow: ${duration}ms`);
}
```

## 🔧 Setup Instructions

### 1. Upstash Redis Setup
1. Create account at [Upstash](https://upstash.com)
2. Create new Redis database
3. Copy REST URL and token to environment variables
4. Test connection:

```bash
curl -X GET \
  -H "Authorization: Bearer YOUR_TOKEN" \
  https://your-redis-url.upstash.io/ping
```

### 2. Security Headers Verification
Test your security headers at:
- [Security Headers](https://securityheaders.com)
- [Mozilla Observatory](https://observatory.mozilla.org)

### 3. CSRF Protection Testing
```javascript
// Test CSRF protection
fetch('/api/protected-endpoint', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-CSRF-Token': 'invalid-token'
  },
  body: JSON.stringify({ test: 'data' })
});
// Should return 403 Forbidden
```

## 🧪 Security Testing

### Automated Security Tests
```bash
# Install security testing tools
npm install --save-dev @security/test-suite

# Run security tests
npm run test:security
```

### Manual Security Checklist
- [ ] Rate limiting works on all API endpoints
- [ ] CSRF tokens are validated on state-changing operations
- [ ] Security headers are present on all responses
- [ ] Authentication is required for protected routes
- [ ] Input validation prevents injection attacks
- [ ] Error messages don't leak sensitive information
- [ ] HTTPS is enforced in production
- [ ] Session management is secure

## 📊 Security Monitoring

### Key Metrics to Monitor
- Rate limit violations per hour
- CSRF validation failures
- Authentication failures
- Slow security middleware responses
- Security header compliance

### Alerting Thresholds
- Rate limit violations > 100/hour
- CSRF failures > 10/hour
- Security middleware > 100ms
- Authentication failures > 50/hour

## 🔄 Maintenance

### Regular Security Tasks
- [ ] Rotate NEXTAUTH_SECRET monthly
- [ ] Update dependencies weekly
- [ ] Review security logs daily
- [ ] Test security headers monthly
- [ ] Audit rate limit configurations quarterly

### Security Updates
- Monitor security advisories for dependencies
- Update Next.js and security-related packages promptly
- Review and update CSP policies as needed
- Test security configurations after updates

## 🆘 Incident Response

### Security Incident Checklist
1. **Immediate Response**
   - Identify the scope of the incident
   - Implement temporary mitigations
   - Document all actions taken

2. **Investigation**
   - Review security logs
   - Identify root cause
   - Assess data impact

3. **Recovery**
   - Implement permanent fixes
   - Update security configurations
   - Verify system integrity

4. **Post-Incident**
   - Update security procedures
   - Conduct lessons learned review
   - Improve monitoring and alerting

## 📚 Additional Resources

- [OWASP Security Guidelines](https://owasp.org)
- [Next.js Security Best Practices](https://nextjs.org/docs/advanced-features/security-headers)
- [Upstash Redis Documentation](https://docs.upstash.com)
- [NextAuth.js Security](https://next-auth.js.org/configuration/options#security)