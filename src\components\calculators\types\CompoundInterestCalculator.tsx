"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export default function CompoundInterestCalculator() {
  const [principal, setPrincipal] = useState<string>("");
  const [interestRate, setInterestRate] = useState<string>("");
  const [timePeriod, setTimePeriod] = useState<string>("");
  const [compoundFrequency, setCompoundFrequency] = useState<string>("12");
  const [monthlyContribution, setMonthlyContribution] = useState<string>("");

  const [futureValue, setFutureValue] = useState<number | null>(null);
  const [totalInterest, setTotalInterest] = useState<number | null>(null);
  const [totalContributions, setTotalContributions] = useState<number | null>(null);

  const compoundingOptions = {
    "1": "Annually",
    "2": "Semi-annually", 
    "4": "Quarterly",
    "12": "Monthly",
    "52": "Weekly",
    "365": "Daily"
  };

  const calculateCompoundInterest = () => {
    const P = parseFloat(principal);
    const r = parseFloat(interestRate) / 100;
    const t = parseFloat(timePeriod);
    const n = parseFloat(compoundFrequency);
    const PMT = parseFloat(monthlyContribution) || 0;

    if (!P || !r || !t || !n) return;

    // Compound interest formula: A = P(1 + r/n)^(nt)
    const compoundAmount = P * Math.pow(1 + r / n, n * t);

    // Future value of annuity (monthly contributions)
    let annuityValue = 0;
    if (PMT > 0) {
      // Convert monthly contribution to match compounding frequency
      const monthsPerPeriod = 12 / n;
      const contributionPerPeriod = PMT * monthsPerPeriod;
      
      // Future value of ordinary annuity formula
      const ratePerPeriod = r / n;
      const totalPeriods = n * t;
      
      if (ratePerPeriod > 0) {
        annuityValue = contributionPerPeriod * ((Math.pow(1 + ratePerPeriod, totalPeriods) - 1) / ratePerPeriod);
      } else {
        annuityValue = contributionPerPeriod * totalPeriods;
      }
    }

    const totalFutureValue = compoundAmount + annuityValue;
    const totalContributed = PMT * 12 * t;
    const totalInterestEarned = totalFutureValue - P - totalContributed;

    setFutureValue(Math.round(totalFutureValue));
    setTotalInterest(Math.round(totalInterestEarned));
    setTotalContributions(Math.round(totalContributed));
  };

  const reset = () => {
    setPrincipal("");
    setInterestRate("");
    setTimePeriod("");
    setMonthlyContribution("");
    setFutureValue(null);
    setTotalInterest(null);
    setTotalContributions(null);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getEffectiveAnnualRate = () => {
    const r = parseFloat(interestRate) / 100;
    const n = parseFloat(compoundFrequency);
    
    if (!r || !n) return 0;
    
    // Effective Annual Rate = (1 + r/n)^n - 1
    return ((Math.pow(1 + r / n, n) - 1) * 100).toFixed(2);
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div>
            <Label htmlFor="principal">Initial Investment ($)</Label>
            <Input
              id="principal"
              type="number"
              value={principal}
              onChange={(e) => setPrincipal(e.target.value)}
              placeholder="Enter initial amount"
            />
          </div>

          <div>
            <Label htmlFor="interestRate">Annual Interest Rate (%)</Label>
            <Input
              id="interestRate"
              type="number"
              step="0.1"
              value={interestRate}
              onChange={(e) => setInterestRate(e.target.value)}
              placeholder="Enter annual interest rate"
            />
          </div>

          <div>
            <Label htmlFor="timePeriod">Time Period (Years)</Label>
            <Input
              id="timePeriod"
              type="number"
              value={timePeriod}
              onChange={(e) => setTimePeriod(e.target.value)}
              placeholder="Enter number of years"
            />
          </div>

          <div>
            <Label htmlFor="compoundFrequency">Compounding Frequency</Label>
            <Select value={compoundFrequency} onValueChange={setCompoundFrequency}>
              <SelectTrigger>
                <SelectValue placeholder="Select compounding frequency" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(compoundingOptions).map(([value, label]) => (
                  <SelectItem key={value} value={value}>{label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="monthlyContribution">Monthly Contribution ($)</Label>
            <Input
              id="monthlyContribution"
              type="number"
              value={monthlyContribution}
              onChange={(e) => setMonthlyContribution(e.target.value)}
              placeholder="Optional monthly addition"
            />
          </div>
        </div>

        <div className="space-y-4">
          <div className="p-4 bg-muted rounded-lg">
            <h3 className="font-semibold mb-2">Investment Summary</h3>
            {principal && (
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Initial Investment:</span>
                  <span className="font-semibold">{formatCurrency(parseFloat(principal))}</span>
                </div>
                {monthlyContribution && timePeriod && (
                  <div className="flex justify-between">
                    <span>Total Contributions:</span>
                    <span className="font-semibold">
                      {formatCurrency(parseFloat(monthlyContribution) * 12 * parseFloat(timePeriod))}
                    </span>
                  </div>
                )}
                {interestRate && compoundFrequency && (
                  <div className="flex justify-between">
                    <span>Effective Annual Rate:</span>
                    <span className="font-semibold">{getEffectiveAnnualRate()}%</span>
                  </div>
                )}
              </div>
            )}
          </div>

          <div className="p-4 bg-primary/10 rounded-lg">
            <h3 className="font-semibold mb-2">Compound Interest Formula</h3>
            <p className="text-sm text-muted-foreground">
              A = P(1 + r/n)^(nt)
            </p>
            <div className="text-xs text-muted-foreground mt-2">
              <p>A = Final amount</p>
              <p>P = Principal amount</p>
              <p>r = Annual interest rate</p>
              <p>n = Compounding frequency</p>
              <p>t = Time in years</p>
            </div>
          </div>

          <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <h3 className="font-semibold mb-2">Power of Compounding</h3>
            <p className="text-sm">
              The earlier you start and the more frequently interest compounds, 
              the more your money grows exponentially over time.
            </p>
          </div>
        </div>
      </div>

      <div className="flex gap-4">
        <Button onClick={calculateCompoundInterest} className="flex-1">
          Calculate Future Value
        </Button>
        <Button onClick={reset} variant="outline">
          Reset
        </Button>
      </div>

      {futureValue !== null && totalInterest !== null && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Future Value</CardTitle>
              <CardDescription>Total amount after {timePeriod} years</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold text-primary">{formatCurrency(futureValue || 0)}</p>
              <p className="text-sm text-muted-foreground mt-2">
                Final investment value
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Total Interest</CardTitle>
              <CardDescription>Interest earned</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold text-primary">{formatCurrency(totalInterest || 0)}</p>
              <p className="text-sm text-muted-foreground mt-2">
                Compound interest gained
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Total Invested</CardTitle>
              <CardDescription>Your contributions</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold text-primary">
                {formatCurrency(parseFloat(principal) + (totalContributions || 0))}
              </p>
              <p className="text-sm text-muted-foreground mt-2">
                Principal + contributions
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {futureValue !== null && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Investment Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span>Initial Investment:</span>
                <span className="font-semibold">{formatCurrency(parseFloat(principal))}</span>
              </div>
              {totalContributions && totalContributions > 0 && (
                <div className="flex justify-between items-center">
                  <span>Monthly Contributions:</span>
                  <span className="font-semibold">{formatCurrency(totalContributions)}</span>
                </div>
              )}
              <div className="flex justify-between items-center">
                <span>Interest Earned:</span>
                <span className="font-semibold">{formatCurrency(totalInterest || 0)}</span>
              </div>
              <div className="border-t pt-2">
                <div className="flex justify-between items-center font-bold">
                  <span>Total Future Value:</span>
                  <span>{formatCurrency(futureValue || 0)}</span>
                </div>
              </div>
              <div className="mt-4">
                <div className="flex justify-between text-sm mb-2">
                  <span>Principal</span>
                  <span>Contributions</span>
                  <span>Interest</span>
                </div>
                <div className="w-full bg-muted rounded-full h-4 flex overflow-hidden">
                  <div 
                    className="bg-blue-500 h-full" 
                    style={{ width: `${(parseFloat(principal) / futureValue) * 100}%` }}
                  ></div>
                  {totalContributions && totalContributions > 0 && (
                    <div 
                      className="bg-green-500 h-full" 
                      style={{ width: `${(totalContributions / (futureValue || 1)) * 100}%` }}
                    ></div>
                  )}
                  <div
                    className="bg-yellow-500 h-full"
                    style={{ width: `${((totalInterest || 0) / (futureValue || 1)) * 100}%` }}
                  ></div>
                </div>
                <div className="flex justify-between text-xs text-muted-foreground mt-1">
                  <span>{((parseFloat(principal) / (futureValue || 1)) * 100).toFixed(1)}%</span>
                  {totalContributions && totalContributions > 0 && (
                    <span>{((totalContributions / (futureValue || 1)) * 100).toFixed(1)}%</span>
                  )}
                  <span>{(((totalInterest || 0) / (futureValue || 1)) * 100).toFixed(1)}%</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
