"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function UnitConverter() {
  const [activeTab, setActiveTab] = useState("length");
  const [inputValue, setInputValue] = useState<string>("");
  const [fromUnit, setFromUnit] = useState<string>("");
  const [toUnit, setToUnit] = useState<string>("");
  const [result, setResult] = useState<number | null>(null);

  // Unit conversion factors (relative to base unit)
  const conversionFactors = {
    length: {
      mm: 0.001,
      cm: 0.01,
      m: 1,
      km: 1000,
      inch: 0.0254,
      ft: 0.3048,
      yd: 0.9144,
      mile: 1609.34
    },
    weight: {
      mg: 0.000001,
      g: 0.001,
      kg: 1,
      ton: 1000,
      oz: 0.0283495,
      lb: 0.453592,
      st: 6.35029
    },
    volume: {
      ml: 0.001,
      l: 1,
      m3: 1000,
      gal: 3.78541,
      qt: 0.946353,
      pt: 0.473176,
      cup: 0.236588
    }
  };

  // Unit labels for display
  const unitLabels = {
    length: {
      mm: "Millimeters (mm)",
      cm: "Centimeters (cm)",
      m: "Meters (m)",
      km: "Kilometers (km)",
      inch: "Inches (in)",
      ft: "Feet (ft)",
      yd: "Yards (yd)",
      mile: "Miles (mi)"
    },
    weight: {
      mg: "Milligrams (mg)",
      g: "Grams (g)",
      kg: "Kilograms (kg)",
      ton: "Metric Tons (t)",
      oz: "Ounces (oz)",
      lb: "Pounds (lb)",
      st: "Stone (st)"
    },
    volume: {
      ml: "Milliliters (ml)",
      l: "Liters (l)",
      m3: "Cubic Meters (m³)",
      gal: "Gallons (gal)",
      qt: "Quarts (qt)",
      pt: "Pints (pt)",
      cup: "Cups"
    }
  };

  const convert = () => {
    if (!inputValue || !fromUnit || !toUnit) return;

    const value = parseFloat(inputValue);
    if (isNaN(value)) return;

    // Convert to base unit, then to target unit
    const category = activeTab as keyof typeof conversionFactors;
    const fromFactor = conversionFactors[category][fromUnit as keyof typeof conversionFactors[typeof category]];
    const toFactor = conversionFactors[category][toUnit as keyof typeof conversionFactors[typeof category]];

    const baseValue = value * fromFactor;
    const convertedValue = baseValue / toFactor;

    setResult(convertedValue);
  };

  // Reset units when changing tabs
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setFromUnit("");
    setToUnit("");
    setResult(null);
  };

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
        <TabsList className="grid grid-cols-3">
          <TabsTrigger value="length">Length</TabsTrigger>
          <TabsTrigger value="weight">Weight</TabsTrigger>
          <TabsTrigger value="volume">Volume</TabsTrigger>
        </TabsList>

        <div className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Unit Converter</CardTitle>
              <CardDescription>
                Convert between different units of {activeTab}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="inputValue">Value</Label>
                  <Input
                    id="inputValue"
                    type="number"
                    placeholder="Enter a value"
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="fromUnit">From</Label>
                    <Select value={fromUnit} onValueChange={setFromUnit}>
                      <SelectTrigger id="fromUnit">
                        <SelectValue placeholder="Select unit" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(unitLabels[activeTab as keyof typeof unitLabels]).map(([key, label]) => (
                          <SelectItem key={key} value={key}>
                            {label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="toUnit">To</Label>
                    <Select value={toUnit} onValueChange={setToUnit}>
                      <SelectTrigger id="toUnit">
                        <SelectValue placeholder="Select unit" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(unitLabels[activeTab as keyof typeof unitLabels]).map(([key, label]) => (
                          <SelectItem key={key} value={key}>
                            {label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <Button
                onClick={convert}
                className="w-full"
                disabled={!inputValue || !fromUnit || !toUnit}
              >
                Convert
              </Button>

              {result !== null && (
                <div className="p-4 bg-primary/10 rounded-lg text-center">
                  <p className="text-sm text-muted-foreground mb-1">Result</p>
                  <p className="text-2xl font-bold text-primary">
                    {inputValue} {(unitLabels as any)[activeTab]?.[fromUnit]?.split(' ')[0] || fromUnit} = {result.toLocaleString(undefined, { maximumFractionDigits: 6 })} {(unitLabels as any)[activeTab]?.[toUnit]?.split(' ')[0] || toUnit}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </Tabs>
    </div>
  );
}
