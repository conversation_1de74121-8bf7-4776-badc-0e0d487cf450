#!/bin/bash

# =============================================================================
# TOOLRAPTER - VPS INFRASTRUCTURE SETUP SCRIPT
# =============================================================================
# Complete VPS setup for Hostinger deployment at toolrapter.com
# Run this script on your VPS as root user

set -e

# Configuration
DOMAIN="toolrapter.com"
APP_NAME="toolrapter"
APP_DIR="/var/www/$APP_NAME"
NGINX_SITES="/etc/nginx/sites-available"
LOG_DIR="/var/log/pm2"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🚀 ToolRapter VPS Infrastructure Setup"
echo "Domain: $DOMAIN"
echo "App Directory: $APP_DIR"
echo "======================================"

# Update system
log_info "Updating system packages..."
apt update && apt upgrade -y

# Install essential packages
log_info "Installing essential packages..."
apt install -y curl wget git unzip software-properties-common apt-transport-https ca-certificates gnupg lsb-release

# Install Node.js 20
log_info "Installing Node.js 20..."
curl -fsSL https://deb.nodesource.com/setup_20.x | bash -
apt install -y nodejs

# Verify Node.js installation
NODE_VERSION=$(node --version)
NPM_VERSION=$(npm --version)
log_success "Node.js $NODE_VERSION and npm $NPM_VERSION installed"

# Install pnpm
log_info "Installing pnpm..."
npm install -g pnpm
PNPM_VERSION=$(pnpm --version)
log_success "pnpm $PNPM_VERSION installed"

# Install PM2
log_info "Installing PM2..."
npm install -g pm2
pm2 install pm2-logrotate
log_success "PM2 installed with log rotation"

# Install Nginx
log_info "Installing Nginx..."
apt install -y nginx
systemctl enable nginx
systemctl start nginx
log_success "Nginx installed and started"

# Install Certbot for SSL
log_info "Installing Certbot for SSL certificates..."
apt install -y certbot python3-certbot-nginx
log_success "Certbot installed"

# Create application directory
log_info "Creating application directory..."
mkdir -p $APP_DIR
mkdir -p $LOG_DIR
mkdir -p /var/backups/$APP_NAME

# Set up firewall
log_info "Configuring firewall..."
ufw allow ssh
ufw allow 'Nginx Full'
ufw allow 80
ufw allow 443
ufw --force enable
log_success "Firewall configured"

# Configure Nginx
log_info "Configuring Nginx..."
cat > $NGINX_SITES/$DOMAIN << 'EOF'
# Rate limiting zones
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=general:10m rate=30r/s;
limit_req_zone $binary_remote_addr zone=static:10m rate=50r/s;

# Upstream configuration
upstream toolrapter_backend {
    least_conn;
    server 127.0.0.1:3000 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

# HTTP to HTTPS redirect
server {
    listen 80;
    listen [::]:80;
    server_name toolrapter.com www.toolrapter.com;
    return 301 https://$server_name$request_uri;
}

# HTTPS server
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name toolrapter.com www.toolrapter.com;

    # SSL Configuration (will be updated by Certbot)
    ssl_certificate /etc/letsencrypt/live/toolrapter.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/toolrapter.com/privkey.pem;
    
    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options SAMEORIGIN always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Logging
    access_log /var/log/nginx/toolrapter_access.log;
    error_log /var/log/nginx/toolrapter_error.log;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # API routes
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://toolrapter_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Static files
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri $uri/ @proxy;
    }
    
    # Main application
    location / {
        limit_req zone=general burst=30 nodelay;
        try_files $uri $uri/ @proxy;
    }
    
    location @proxy {
        proxy_pass http://toolrapter_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
EOF

# Enable the site
ln -sf $NGINX_SITES/$DOMAIN /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# Test Nginx configuration
nginx -t
systemctl reload nginx
log_success "Nginx configured and reloaded"

# Set up SSL certificate
log_info "Setting up SSL certificate..."
certbot --nginx -d $DOMAIN -d www.$DOMAIN --non-interactive --agree-tos --email admin@$DOMAIN
log_success "SSL certificate installed"

# Set up automatic SSL renewal
log_info "Setting up automatic SSL renewal..."
echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -
log_success "SSL auto-renewal configured"

# Configure PM2 startup
log_info "Configuring PM2 startup..."
pm2 startup systemd -u root --hp /root
log_success "PM2 startup configured"

# Create deployment user (optional)
log_info "Creating deployment user..."
useradd -m -s /bin/bash deploy || true
usermod -aG sudo deploy || true
log_success "Deployment user created"

# Set up log rotation
log_info "Setting up log rotation..."
cat > /etc/logrotate.d/toolrapter << 'EOF'
/var/log/pm2/*.log {
    daily
    missingok
    rotate 52
    compress
    notifempty
    create 644 root root
    postrotate
        pm2 reloadLogs
    endscript
}
EOF

log_success "Log rotation configured"

# Final system optimization
log_info "Applying system optimizations..."
echo "net.core.somaxconn = 65535" >> /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 65535" >> /etc/sysctl.conf
sysctl -p

log_success "System optimizations applied"

echo "======================================"
log_success "VPS infrastructure setup completed!"
echo ""
echo "📋 Next Steps:"
echo "1. Configure GitHub repository secrets"
echo "2. Push code to trigger deployment"
echo "3. Monitor deployment in GitHub Actions"
echo "4. Verify site at https://$DOMAIN"
echo ""
echo "🔧 Useful Commands:"
echo "- Check PM2 status: pm2 status"
echo "- View logs: pm2 logs"
echo "- Restart app: pm2 restart $APP_NAME"
echo "- Check Nginx: systemctl status nginx"
echo "- SSL renewal test: certbot renew --dry-run"
