# =============================================================================
# TOOLRAPTER - NGINX CONFIGURATION
# =============================================================================
# Enterprise-grade Nginx configuration for Hostinger VPS
# Domain: toolrapter.com | VPS: ************
# Place this file at: /etc/nginx/sites-available/toolrapter.com
# Create symlink: ln -s /etc/nginx/sites-available/toolrapter.com /etc/nginx/sites-enabled/

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=general:10m rate=30r/s;
limit_req_zone $binary_remote_addr zone=static:10m rate=50r/s;

# Upstream configuration for PM2 cluster
upstream toolrapter_backend {
    least_conn;
    server 127.0.0.1:3000 max_fails=3 fail_timeout=30s;
    # Add more servers if running multiple instances
    # server 127.0.0.1:3001 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

# HTTP to HTTPS redirect
server {
    listen 80;
    listen [::]:80;
    server_name toolrapter.com www.toolrapter.com;
    
    # Security headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # Redirect all HTTP traffic to HTTPS
    return 301 https://$server_name$request_uri;
}

# HTTPS server configuration
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name toolrapter.com www.toolrapter.com;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/toolrapter.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/toolrapter.com/privkey.pem;
    ssl_trusted_certificate /etc/letsencrypt/live/toolrapter.com/chain.pem;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;
    ssl_stapling on;
    ssl_stapling_verify on;
    
    # Enterprise Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options SAMEORIGIN always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Permissions-Policy "camera=(), microphone=(), geolocation=(), interest-cohort=()" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com data:; img-src 'self' data: blob: https: http:; connect-src 'self' https: wss:; frame-src 'self'; object-src 'none'; base-uri 'self'; form-action 'self'; upgrade-insecure-requests;" always;
    add_header X-Robots-Tag "index, follow" always;

    # Basic Settings
    root /var/www/toolrapter;
    index index.html index.htm;

    # Logging
    access_log /var/log/nginx/toolrapter_access.log;
    error_log /var/log/nginx/toolrapter_error.log;
    
    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Static files caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff;
        
        # Rate limiting for static files
        limit_req zone=static burst=20 nodelay;
        
        # Try to serve static files directly
        try_files $uri $uri/ @proxy;
    }
    
    # Next.js static files
    location /_next/static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri $uri/ @proxy;
    }
    
    # API routes with rate limiting
    location /api/ {
        # Rate limiting
        limit_req zone=api burst=20 nodelay;
        limit_req_status 429;
        
        # Security headers for API
        add_header X-Content-Type-Options nosniff always;
        add_header X-Frame-Options DENY always;
        
        # Proxy to Next.js
        proxy_pass http://toolrapter_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Admin routes with additional security
    location /admin/ {
        # Rate limiting
        limit_req zone=general burst=10 nodelay;
        
        # Additional security headers
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;
        
        # Proxy to Next.js
        proxy_pass http://toolrapter_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Health check endpoint
    location /api/health {
        access_log off;
        proxy_pass http://toolrapter_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Main application proxy
    location / {
        # Rate limiting
        limit_req zone=general burst=30 nodelay;
        
        # Try static files first, then proxy
        try_files $uri $uri/ @proxy;
    }
    
    # Proxy fallback
    location @proxy {
        proxy_pass http://toolrapter_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
    }
    
    # Security: Block access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ /(\.env|\.git|package\.json|ecosystem\.config\.js) {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Robots.txt
    location = /robots.txt {
        allow all;
        log_not_found off;
        access_log off;
    }
    
    # Favicon
    location = /favicon.ico {
        log_not_found off;
        access_log off;
    }
}

# =============================================================================
# NGINX COMMANDS REFERENCE
# =============================================================================
# 
# Test configuration:
# sudo nginx -t
# 
# Reload configuration:
# sudo systemctl reload nginx
# 
# Restart Nginx:
# sudo systemctl restart nginx
# 
# Check status:
# sudo systemctl status nginx
# 
# View logs:
# sudo tail -f /var/log/nginx/toolcrush_access.log
# sudo tail -f /var/log/nginx/toolcrush_error.log
# 
# Enable site:
# sudo ln -s /etc/nginx/sites-available/toolcrush /etc/nginx/sites-enabled/
# 
# SSL certificate renewal (Let's Encrypt):
# sudo certbot renew --dry-run
# sudo certbot renew
