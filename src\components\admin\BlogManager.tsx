"use client";

import { useState, useMemo, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { getAllPosts, getCategories, getTags } from "@/services/blogService";
import { PlusCircle, Edit, Trash2, Tag, Calendar, User, FileText } from "lucide-react";

// Add searchFilter prop to component
interface BlogManagerProps {
  searchFilter?: string;
}

export function BlogManager({ searchFilter = "" }: BlogManagerProps) {
  // Use useMemo to prevent unnecessary recalculations
  const initialPosts = useMemo(() => getAllPosts(), []);
  const categories = useMemo(() => getCategories(), []);
  const tags = useMemo(() => getTags(), []);

  const [posts, setPosts] = useState(initialPosts);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedPost, setSelectedPost] = useState<any>(null);

  // Memoized callbacks for dialog open state changes
  const handleCreateDialogOpenChange = useCallback((open: boolean) => {
    setIsCreateDialogOpen(open);
  }, []);

  const handleEditDialogOpenChange = useCallback((open: boolean) => {
    setIsEditDialogOpen(open);
  }, []);

  const [newPost, setNewPost] = useState({
    title: "",
    content: "",
    description: "",
    category: "",
    image: "",
    tags: [] as string[]
  });

  const handleCreatePost = () => {
    // In a real app, this would send data to the server
    const today = new Date();
    const formattedDate = `${today.toLocaleString('default', { month: 'long' })} ${today.getDate()}, ${today.getFullYear()}`;

    const newPostWithId = {
      ...newPost,
      id: newPost.title.toLowerCase().replace(/\s+/g, '-'),
      date: formattedDate,
      author: "Admin User"
    };

    setPosts([newPostWithId, ...posts]);
    setNewPost({
      title: "",
      content: "",
      description: "",
      category: "",
      image: "",
      tags: []
    });
    setIsCreateDialogOpen(false);
  };

  const handleEditPost = () => {
    if (!selectedPost) return;

    const updatedPosts = posts.map(post =>
      post.id === selectedPost.id ? selectedPost : post
    );

    setPosts(updatedPosts);
    setSelectedPost(null);
    setIsEditDialogOpen(false);
  };

  const handleDeletePost = (id: string) => {
    const updatedPosts = posts.filter(post => post.id !== id);
    setPosts(updatedPosts);
  };

  const openEditDialog = (post: any) => {
    setSelectedPost(post);
    setIsEditDialogOpen(true);
  };

  // Filter posts based on search term
  const filteredPosts = posts.filter(post =>
    searchFilter ?
      post.title.toLowerCase().includes(searchFilter.toLowerCase()) ||
      post.description?.toLowerCase().includes(searchFilter.toLowerCase()) :
      true
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Blog Management</h2>
        <div className="flex gap-2">
          <Button className="flex items-center gap-2" asChild>
            <a href="/admin/blog/editor">
              <PlusCircle className="h-4 w-4" />
              New Post
            </a>
          </Button>
          <Dialog
            open={isCreateDialogOpen}
            onOpenChange={handleCreateDialogOpenChange}
          >
            <DialogTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <PlusCircle className="h-4 w-4" />
                New Post (Simple)
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-3xl">
              <DialogHeader>
                <DialogTitle>Create New Blog Post</DialogTitle>
                <DialogDescription>
                  Fill in the details to create a new blog post.
                </DialogDescription>
              </DialogHeader>

            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    value={newPost.title}
                    onChange={(e) => setNewPost({...newPost, title: e.target.value})}
                    placeholder="Enter post title"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <Select
                    onValueChange={(value) => setNewPost({...newPost, category: value})}
                    value={newPost.category || undefined}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map(category => (
                        <SelectItem key={category.name} value={category.name}>
                          {category.name}
                        </SelectItem>
                      ))}
                      <SelectItem value="New Category">+ Add New Category</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={newPost.description}
                  onChange={(e) => setNewPost({...newPost, description: e.target.value})}
                  placeholder="Enter post description"
                  rows={2}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="image">Image URL</Label>
                <Input
                  id="image"
                  value={newPost.image}
                  onChange={(e) => setNewPost({...newPost, image: e.target.value})}
                  placeholder="Enter image URL"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="content">Content (HTML)</Label>
                <Textarea
                  id="content"
                  value={newPost.content}
                  onChange={(e) => setNewPost({...newPost, content: e.target.value})}
                  placeholder="Enter post content in HTML"
                  rows={10}
                />
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>Cancel</Button>
              <Button onClick={handleCreatePost}>Create Post</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>

    <Tabs defaultValue="all">
      <TabsList>
        <TabsTrigger value="all">All Posts ({filteredPosts.length})</TabsTrigger>
        <TabsTrigger value="published">Published</TabsTrigger>
        <TabsTrigger value="drafts">Drafts</TabsTrigger>
      </TabsList>

      <TabsContent value="all" className="space-y-4">
        {filteredPosts.length === 0 ? (
          <div className="text-center py-10 text-adaptive-muted">
            <FileText className="h-12 w-12 mx-auto mb-2 opacity-20" />
            <p>No posts found</p>
          </div>
        ) : (
          filteredPosts.map(post => (
            <Card key={post.id}>
              <CardHeader className="pb-2">
                <div className="flex justify-between">
                  <CardTitle>{post.title}</CardTitle>
                  <div className="flex gap-2">
                    <Button variant="outline" size="icon" asChild>
                      <a href={`/admin/blog/edit/${post.id}`}>
                        <Edit className="h-4 w-4" />
                      </a>
                    </Button>
                    <Button variant="destructive" size="icon" onClick={() => handleDeletePost(post.id)}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <CardDescription className="flex items-center gap-4 text-xs">
                  <span className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {post.date}
                  </span>
                  <span className="flex items-center gap-1">
                    <User className="h-3 w-3" />
                    {post.author}
                  </span>
                  <span className="flex items-center gap-1">
                    <Tag className="h-3 w-3" />
                    {post.category}
                  </span>
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground line-clamp-2">{post.description}</p>
              </CardContent>
              <CardFooter className="pt-0">
                <Button variant="link" className="px-0" asChild>
                  <a href={`/blog/${post.id}`} target="_blank">View Post</a>
                </Button>
              </CardFooter>
            </Card>
          ))
        )}
      </TabsContent>

      <TabsContent value="published">
        <p className="text-muted-foreground">Published posts will appear here.</p>
      </TabsContent>

      <TabsContent value="drafts">
        <p className="text-muted-foreground">Draft posts will appear here.</p>
      </TabsContent>
    </Tabs>

    {/* Edit Dialog */}
    <Dialog
      open={isEditDialogOpen}
      onOpenChange={handleEditDialogOpenChange}
    >
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>Edit Blog Post</DialogTitle>
          <DialogDescription>
            Update the details of your blog post.
          </DialogDescription>
        </DialogHeader>

        {selectedPost && (
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-title">Title</Label>
                <Input
                  id="edit-title"
                  value={selectedPost.title}
                  onChange={(e) => setSelectedPost({...selectedPost, title: e.target.value})}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-category">Category</Label>
                <Select
                  onValueChange={(value) => setSelectedPost({...selectedPost, category: value})}
                  value={selectedPost.category || undefined}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map(category => (
                      <SelectItem key={category.name} value={category.name}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-description">Description</Label>
              <Textarea
                id="edit-description"
                value={selectedPost.description || ""}
                onChange={(e) => setSelectedPost({...selectedPost, description: e.target.value})}
                rows={2}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-image">Image URL</Label>
              <Input
                id="edit-image"
                value={selectedPost.image}
                onChange={(e) => setSelectedPost({...selectedPost, image: e.target.value})}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-content">Content (HTML)</Label>
              <Textarea
                id="edit-content"
                value={selectedPost.content}
                onChange={(e) => setSelectedPost({...selectedPost, content: e.target.value})}
                rows={10}
              />
            </div>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleEditPost}>Save Changes</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
  );
}

