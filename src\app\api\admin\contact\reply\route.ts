import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import Contact from '@/models/Contact';
import { z } from 'zod';
import nodemailer from 'nodemailer';

// Validation schema for reply request
const replySchema = z.object({
  contactId: z.string().min(1, 'Contact ID is required'),
  subject: z.string().min(1, 'Subject is required'),
  message: z.string().min(1, 'Message is required'),
  ccAdmin: z.boolean().optional().default(false),
  recipientEmail: z.string().email('Valid email is required'),
  recipientName: z.string().min(1, 'Recipient name is required'),
});

// Email transporter configuration
const createTransporter = () => {
  return nodemailer.createTransport({
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: false, // true for 465, false for other ports
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  });
};

// Email template for reply
const createReplyEmailTemplate = (
  recipientName: string,
  message: string,
  adminName: string,
  originalMessage: string
) => {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Reply from ToolBox Support</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .content { background: white; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px; }
        .footer { margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 8px; font-size: 14px; color: #666; }
        .original-message { background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin-top: 20px; }
        .signature { margin-top: 20px; border-top: 1px solid #e9ecef; padding-top: 15px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h2 style="margin: 0; color: #007bff;">ToolBox Support Team</h2>
          <p style="margin: 5px 0 0 0; color: #666;">Reply to your inquiry</p>
        </div>
        
        <div class="content">
          <p>Dear ${recipientName},</p>
          
          <div style="margin: 20px 0;">
            ${message.replace(/\n/g, '<br>')}
          </div>
          
          <div class="signature">
            <p>Best regards,<br>
            ${adminName}<br>
            ToolBox Support Team</p>
          </div>
          
          <div class="original-message">
            <h4 style="margin: 0 0 10px 0; color: #666;">Your Original Message:</h4>
            <p style="margin: 0; font-style: italic;">${originalMessage}</p>
          </div>
        </div>
        
        <div class="footer">
          <p><strong>ToolBox</strong> - Your Digital Toolkit</p>
          <p>This email was sent in response to your contact form submission. If you have any additional questions, please don't hesitate to reach out.</p>
        </div>
      </div>
    </body>
    </html>
  `;
};

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = replySchema.parse(body);

    // Connect to database
    await connectDB();

    // Find the contact
    const contact = await Contact.findById(validatedData.contactId);
    if (!contact) {
      return NextResponse.json(
        { success: false, error: 'Contact not found' },
        { status: 404 }
      );
    }

    // Create email transporter
    const transporter = createTransporter();

    // Verify transporter configuration
    try {
      await transporter.verify();
    } catch (error) {
      console.error('Email transporter verification failed:', error);
      return NextResponse.json(
        { success: false, error: 'Email service configuration error' },
        { status: 500 }
      );
    }

    // Prepare email content
    const emailHtml = createReplyEmailTemplate(
      validatedData.recipientName,
      validatedData.message,
      session.user.name || 'Support Team',
      contact.message
    );

    // Email options
    const mailOptions: any = {
      from: {
        name: 'ToolBox Support',
        address: process.env.SMTP_FROM || process.env.SMTP_USER || '<EMAIL>',
      },
      to: validatedData.recipientEmail,
      subject: validatedData.subject,
      html: emailHtml,
      text: validatedData.message, // Plain text fallback
    };

    // Add CC if requested
    if (validatedData.ccAdmin && process.env.SMTP_USER) {
      mailOptions.cc = process.env.SMTP_USER;
    }

    // Send email
    const emailResult = await transporter.sendMail(mailOptions);

    // Update contact record
    const updateData = {
      responseSent: true,
      status: 'resolved',
      updatedAt: new Date(),
      $push: {
        replies: {
          subject: validatedData.subject,
          message: validatedData.message,
          sentBy: session.user.id,
          sentAt: new Date(),
          emailId: emailResult.messageId,
        },
      },
    };

    await Contact.findByIdAndUpdate(validatedData.contactId, updateData);

    return NextResponse.json({
      success: true,
      message: 'Reply sent successfully',
      emailId: emailResult.messageId,
    });

  } catch (error) {
    console.error('Error sending reply:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation error',
          details: error.errors 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
