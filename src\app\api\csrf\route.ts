import { NextRequest, NextResponse } from 'next/server';
import { generateAndSetCSRFToken } from '@/lib/csrf';

/**
 * GET /api/csrf
 * Generates and returns a CSRF token for authenticated users
 * The token is also set as an httpOnly cookie for validation
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated (admin or regular user)
    const userRole = request.headers.get('x-user-role');
    const userId = request.headers.get('x-user-id');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    // Create response
    const response = NextResponse.json({
      success: true,
      message: 'CSRF token generated',
    });
    
    // Generate and set CSRF token
    const token = await generateAndSetCSRFToken(response);
    
    // Return the token in the response body for client-side use
    return NextResponse.json({
      success: true,
      token,
      message: 'CSRF token generated successfully',
    }, {
      headers: response.headers,
    });
    
  } catch (error) {
    console.error('CSRF token generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate CSRF token' },
      { status: 500 }
    );
  }
}
