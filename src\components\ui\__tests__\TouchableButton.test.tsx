import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { TouchableButton } from '../TouchableButton'

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    button: React.forwardRef<HTMLButtonElement, any>(({ children, onClick, whileTap, animate, ...props }, ref) => (
      <button ref={ref} onClick={onClick} {...props}>
        {children}
      </button>
    )),
  },
}))

// Mock useTouch hook
jest.mock('@/hooks/useTouch', () => ({
  useTouch: () => ({
    touchState: {
      isPressed: false,
      isLongPressed: false,
      isSwiping: false,
      swipeDirection: null,
      touchStartTime: null,
    },
    touchHandlers: {
      onTouchStart: jest.fn(),
      onTouchEnd: jest.fn(),
    },
    isTouchDevice: false,
  }),
  hapticFeedback: {
    light: jest.fn(),
    medium: jest.fn(),
    heavy: jest.fn(),
    success: jest.fn(),
    error: jest.fn(),
  },
}))

describe('TouchableButton', () => {
  it('should render with children', () => {
    render(<TouchableButton>Test Button</TouchableButton>)
    expect(screen.getByText('Test Button')).toBeInTheDocument()
  })

  it('should handle click events', () => {
    const onClick = jest.fn()
    render(<TouchableButton onClick={onClick}>Test Button</TouchableButton>)

    fireEvent.click(screen.getByText('Test Button'))
    expect(onClick).toHaveBeenCalled()
  })

  it('should be disabled when disabled prop is true', () => {
    const onClick = jest.fn()
    render(
      <TouchableButton disabled onClick={onClick}>
        Test Button
      </TouchableButton>
    )

    const button = screen.getByText('Test Button')
    expect(button).toBeDisabled()

    fireEvent.click(button)
    expect(onClick).not.toHaveBeenCalled()
  })

  it('should apply correct variant classes', () => {
    const { rerender } = render(
      <TouchableButton variant="primary">Primary Button</TouchableButton>
    )
    
    let button = screen.getByText('Primary Button')
    expect(button).toHaveClass('bg-primary')
    
    rerender(<TouchableButton variant="secondary">Secondary Button</TouchableButton>)
    button = screen.getByText('Secondary Button')
    expect(button).toHaveClass('bg-secondary')
  })

  it('should apply correct size classes', () => {
    const { rerender } = render(
      <TouchableButton size="sm">Small Button</TouchableButton>
    )

    let button = screen.getByText('Small Button')
    expect(button).toHaveClass('h-9')

    rerender(<TouchableButton size="lg">Large Button</TouchableButton>)
    button = screen.getByText('Large Button')
    expect(button).toHaveClass('h-11')
  })

  it('should handle long press events', () => {
    const onLongPress = jest.fn()
    render(
      <TouchableButton onLongPress={onLongPress}>
        Test Button
      </TouchableButton>
    )
    
    // Note: Testing long press would require more complex setup with timers
    // This is a basic structure for the test
    expect(screen.getByText('Test Button')).toBeInTheDocument()
  })
})
