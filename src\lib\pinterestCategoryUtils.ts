import {
  Code,
  Palette,
  Brain,
  Briefcase,
  Heart,
  DollarSign,
  Plane,
  Utensils,
  Car,
  Gamepad2,
  Music,
  Camera,
  Book,
  Zap,
  Globe,
  Wrench,
  type LucideProps
} from 'lucide-react';

// Define LucideIcon type for compatibility
export type LucideIcon = React.ComponentType<LucideProps>;

export interface PinterestCategoryConfig {
  name: string;
  color: string;
  bgColor: string;
  textColor: string;
  borderColor: string;
  icon: LucideIcon;
  description?: string;
}

// Pinterest-style category configuration with enhanced visual styling
export const pinterestCategoryConfigs: Record<string, PinterestCategoryConfig> = {
  'Technology': {
    name: 'Technology',
    color: 'blue',
    bgColor: 'bg-blue-500/90',
    textColor: 'text-white',
    borderColor: 'border-blue-400/50',
    icon: Code,
    description: 'Latest tech trends and innovations'
  },
  'Tech': {
    name: 'Tech',
    color: 'blue',
    bgColor: 'bg-blue-500/90',
    textColor: 'text-white',
    borderColor: 'border-blue-400/50',
    icon: Code,
    description: 'Technology and development'
  },
  'AI': {
    name: 'AI',
    color: 'purple',
    bgColor: 'bg-purple-500/90',
    textColor: 'text-white',
    borderColor: 'border-purple-400/50',
    icon: Brain,
    description: 'Artificial Intelligence and Machine Learning'
  },
  'Design': {
    name: 'Design',
    color: 'pink',
    bgColor: 'bg-pink-500/90',
    textColor: 'text-white',
    borderColor: 'border-pink-400/50',
    icon: Palette,
    description: 'UI/UX Design and Creative Arts'
  },
  'Development': {
    name: 'Development',
    color: 'green',
    bgColor: 'bg-green-500/90',
    textColor: 'text-white',
    borderColor: 'border-green-400/50',
    icon: Code,
    description: 'Software Development and Programming'
  },
  'Business': {
    name: 'Business',
    color: 'orange',
    bgColor: 'bg-orange-500/90',
    textColor: 'text-white',
    borderColor: 'border-orange-400/50',
    icon: Briefcase,
    description: 'Business Strategy and Entrepreneurship'
  },
  'Lifestyle': {
    name: 'Lifestyle',
    color: 'teal',
    bgColor: 'bg-teal-500/90',
    textColor: 'text-white',
    borderColor: 'border-teal-400/50',
    icon: Heart,
    description: 'Life, Wellness and Personal Growth'
  },
  'Health': {
    name: 'Health',
    color: 'red',
    bgColor: 'bg-red-500/90',
    textColor: 'text-white',
    borderColor: 'border-red-400/50',
    icon: Heart,
    description: 'Health, Fitness and Medical'
  },
  'Finance': {
    name: 'Finance',
    color: 'yellow',
    bgColor: 'bg-yellow-500/90',
    textColor: 'text-black',
    borderColor: 'border-yellow-400/50',
    icon: DollarSign,
    description: 'Finance, Investment and Economics'
  },
  'Travel': {
    name: 'Travel',
    color: 'indigo',
    bgColor: 'bg-indigo-500/90',
    textColor: 'text-white',
    borderColor: 'border-indigo-400/50',
    icon: Plane,
    description: 'Travel, Adventure and Culture'
  },
  'Food': {
    name: 'Food',
    color: 'amber',
    bgColor: 'bg-amber-500/90',
    textColor: 'text-white',
    borderColor: 'border-amber-400/50',
    icon: Utensils,
    description: 'Food, Cooking and Culinary Arts'
  },
  'Automotive': {
    name: 'Automotive',
    color: 'gray',
    bgColor: 'bg-gray-500/90',
    textColor: 'text-white',
    borderColor: 'border-gray-400/50',
    icon: Car,
    description: 'Cars, Vehicles and Transportation'
  },
  'Gaming': {
    name: 'Gaming',
    color: 'violet',
    bgColor: 'bg-violet-500/90',
    textColor: 'text-white',
    borderColor: 'border-violet-400/50',
    icon: Gamepad2,
    description: 'Gaming, Esports and Entertainment'
  },
  'Music': {
    name: 'Music',
    color: 'rose',
    bgColor: 'bg-rose-500/90',
    textColor: 'text-white',
    borderColor: 'border-rose-400/50',
    icon: Music,
    description: 'Music, Audio and Sound'
  },
  'Photography': {
    name: 'Photography',
    color: 'cyan',
    bgColor: 'bg-cyan-500/90',
    textColor: 'text-white',
    borderColor: 'border-cyan-400/50',
    icon: Camera,
    description: 'Photography and Visual Arts'
  },
  'Education': {
    name: 'Education',
    color: 'emerald',
    bgColor: 'bg-emerald-500/90',
    textColor: 'text-white',
    borderColor: 'border-emerald-400/50',
    icon: Book,
    description: 'Education, Learning and Teaching'
  },
  'Science': {
    name: 'Science',
    color: 'sky',
    bgColor: 'bg-sky-500/90',
    textColor: 'text-white',
    borderColor: 'border-sky-400/50',
    icon: Zap,
    description: 'Science, Research and Innovation'
  },
  'Web': {
    name: 'Web',
    color: 'lime',
    bgColor: 'bg-lime-500/90',
    textColor: 'text-black',
    borderColor: 'border-lime-400/50',
    icon: Globe,
    description: 'Web Development and Internet'
  },
  'Tools': {
    name: 'Tools',
    color: 'stone',
    bgColor: 'bg-stone-500/90',
    textColor: 'text-white',
    borderColor: 'border-stone-400/50',
    icon: Wrench,
    description: 'Tools, Utilities and Resources'
  },
  'General': {
    name: 'General',
    color: 'slate',
    bgColor: 'bg-slate-500/90',
    textColor: 'text-white',
    borderColor: 'border-slate-400/50',
    icon: Book,
    description: 'General topics and miscellaneous'
  }
};

// Utility functions for Pinterest-style categories
export const getPinterestCategoryConfig = (category: string = 'General'): PinterestCategoryConfig => {
  return pinterestCategoryConfigs[category] || pinterestCategoryConfigs['General'];
};

export const getPinterestCategoryColor = (category: string = 'General'): string => {
  const config = getPinterestCategoryConfig(category);
  return `${config.bgColor} ${config.textColor} ${config.borderColor}`;
};

export const getPinterestCategoryIcon = (category: string = 'General'): LucideIcon => {
  const config = getPinterestCategoryConfig(category);
  return config.icon;
};

export const getAllPinterestCategories = (): PinterestCategoryConfig[] => {
  return Object.values(pinterestCategoryConfigs);
};

export const getPinterestCategoryNames = (): string[] => {
  return Object.keys(pinterestCategoryConfigs);
};

// Generate Pinterest-style category badge classes for Tailwind
export const getPinterestCategoryBadgeClasses = (category: string = 'General'): string => {
  const config = getPinterestCategoryConfig(category);
  return `${config.bgColor} ${config.textColor} ${config.borderColor} backdrop-blur-md shadow-lg`;
};

// Generate Pinterest-style category hover classes
export const getPinterestCategoryHoverClasses = (category: string = 'General'): string => {
  const config = getPinterestCategoryConfig(category);
  const hoverColor = config.color;
  return `hover:bg-${hoverColor}-600/90 hover:border-${hoverColor}-500/60`;
};

// Normalize category name for Pinterest styling (handle variations)
export const normalizePinterestCategoryName = (category: string): string => {
  const normalized = category.trim();
  
  // Handle common variations
  const variations: Record<string, string> = {
    'tech': 'Technology',
    'ai': 'AI',
    'ml': 'AI',
    'machine learning': 'AI',
    'artificial intelligence': 'AI',
    'ui': 'Design',
    'ux': 'Design',
    'ui/ux': 'Design',
    'dev': 'Development',
    'programming': 'Development',
    'coding': 'Development',
    'biz': 'Business',
    'startup': 'Business',
    'entrepreneur': 'Business',
    'wellness': 'Health',
    'fitness': 'Health',
    'medical': 'Health',
    'money': 'Finance',
    'investment': 'Finance',
    'crypto': 'Finance',
    'blockchain': 'Finance',
    'cars': 'Automotive',
    'vehicles': 'Automotive',
    'auto': 'Automotive',
    'games': 'Gaming',
    'esports': 'Gaming',
    'photo': 'Photography',
    'pics': 'Photography',
    'learn': 'Education',
    'tutorial': 'Education',
    'course': 'Education'
  };

  return variations[normalized.toLowerCase()] || 
         pinterestCategoryConfigs[normalized] ? normalized : 'General';
};

// Pinterest-specific utility hook for category styling
export const usePinterestCategoryStyle = (category?: string) => {
  const normalizedCategory = normalizePinterestCategoryName(category || 'General');
  const config = getPinterestCategoryConfig(normalizedCategory);
  
  return {
    name: config.name,
    color: config.color,
    bgColor: config.bgColor,
    textColor: config.textColor,
    borderColor: config.borderColor,
    icon: config.icon,
    description: config.description,
    badgeClasses: getPinterestCategoryBadgeClasses(normalizedCategory),
    hoverClasses: getPinterestCategoryHoverClasses(normalizedCategory)
  };
};

// Generate gradient backgrounds for Pinterest cards
export const getPinterestCategoryGradient = (category: string = 'General'): string => {
  const gradients: Record<string, string> = {
    'Technology': 'bg-gradient-to-br from-blue-500 to-blue-700',
    'Tech': 'bg-gradient-to-br from-blue-500 to-blue-700',
    'AI': 'bg-gradient-to-br from-purple-500 to-purple-700',
    'Design': 'bg-gradient-to-br from-pink-500 to-pink-700',
    'Development': 'bg-gradient-to-br from-green-500 to-green-700',
    'Business': 'bg-gradient-to-br from-orange-500 to-orange-700',
    'Lifestyle': 'bg-gradient-to-br from-teal-500 to-teal-700',
    'Health': 'bg-gradient-to-br from-red-500 to-red-700',
    'Finance': 'bg-gradient-to-br from-yellow-400 to-yellow-600',
    'Travel': 'bg-gradient-to-br from-indigo-500 to-indigo-700',
    'Food': 'bg-gradient-to-br from-amber-500 to-amber-700',
    'Automotive': 'bg-gradient-to-br from-gray-500 to-gray-700',
    'Gaming': 'bg-gradient-to-br from-violet-500 to-violet-700',
    'Music': 'bg-gradient-to-br from-rose-500 to-rose-700',
    'Photography': 'bg-gradient-to-br from-cyan-500 to-cyan-700',
    'Education': 'bg-gradient-to-br from-emerald-500 to-emerald-700',
    'Science': 'bg-gradient-to-br from-sky-500 to-sky-700',
    'Web': 'bg-gradient-to-br from-lime-500 to-lime-700',
    'Tools': 'bg-gradient-to-br from-stone-500 to-stone-700',
    'General': 'bg-gradient-to-br from-slate-500 to-slate-700'
  };
  
  return gradients[category] || gradients['General'];
};
