# GitHub Repository Secrets Setup Guide

This document provides comprehensive instructions for setting up all required GitHub repository secrets for the ToolCrush deployment pipeline.

## ⚠️ Critical Notice

**All "Context access might be invalid" warnings in the GitHub Actions workflow will be resolved once these secrets are properly configured in your repository settings.**

## 🔐 Required Secrets Overview

The following secrets must be configured in your GitHub repository before the CI/CD pipeline can function properly:

| Secret Name | Description | Required | Format |
|-------------|-------------|----------|---------|
| `VPS_SSH_KEY` | Ed25519 private SSH key for VPS access | ✅ | PEM format |
| `VPS_USER` | VPS username for SSH connection | ✅ | String |
| `VPS_HOST` | VPS IP address or domain name | ✅ | IP/FQDN |
| `MONGODB_URI` | MongoDB Atlas connection string | ✅ | mongodb+srv://... |
| `NEXTAUTH_SECRET` | NextAuth.js secret key | ✅ | Base64 string |
| `NEXTAUTH_URL` | Application URL | ✅ | https://toolrapter.com |
| `GOOGLE_CLIENT_ID` | Google OAuth client ID | ⚠️ Optional | String |
| `GOOGLE_CLIENT_SECRET` | Google OAuth client secret | ⚠️ Optional | String |

**REMOVED SECRETS (No longer needed):**
- ❌ `UPSTASH_REDIS_REST_URL` - Using in-memory rate limiting instead
- ❌ `UPSTASH_REDIS_REST_TOKEN` - Using in-memory rate limiting instead

## 📋 Step-by-Step Setup Instructions

### 1. Access GitHub Repository Secrets

1. Navigate to your GitHub repository: `https://github.com/MuhammadShahbaz195/ToolCrush`
2. Click on **Settings** tab
3. In the left sidebar, click **Secrets and variables** → **Actions**
4. Click **Repository secrets** tab
5. Click **New repository secret** for each secret below

### 2. VPS Authentication Secrets

#### VPS_SSH_KEY
```bash
# Generate Ed25519 SSH key pair (if not already done)
ssh-keygen -t ed25519 -C "github-actions@toolcrush" -f ~/.ssh/toolcrush_ed25519

# Copy the PRIVATE key content (including headers)
cat ~/.ssh/toolcrush_ed25519
```
- **Name**: `VPS_SSH_KEY`
- **Value**: Complete private key content including `-----BEGIN OPENSSH PRIVATE KEY-----` and `-----END OPENSSH PRIVATE KEY-----`

#### VPS_USER
- **Name**: `VPS_USER`
- **Value**: `root` (or your VPS username)

#### VPS_HOST
- **Name**: `VPS_HOST`
- **Value**: `************` (or your VPS IP address)

### 3. Database Connection Secrets

#### MONGODB_URI
```bash
# Format: mongodb+srv://username:<EMAIL>/database?retryWrites=true&w=majority
```
- **Name**: `MONGODB_URI`
- **Value**: Your MongoDB Atlas connection string
- **Example**: `mongodb+srv://user:<EMAIL>/toolcrush?retryWrites=true&w=majority`

### 4. Authentication Secrets

#### NEXTAUTH_SECRET
```bash
# Generate a secure random secret
openssl rand -base64 32
```
- **Name**: `NEXTAUTH_SECRET`
- **Value**: Generated base64 string (32+ characters)

#### Google OAuth Credentials
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create or select a project
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `https://toolrapter.com/api/auth/callback/google`
   - `http://localhost:3000/api/auth/callback/google` (for development)

- **Name**: `GOOGLE_CLIENT_ID`
- **Value**: Your Google OAuth client ID

- **Name**: `GOOGLE_CLIENT_SECRET`
- **Value**: Your Google OAuth client secret

### 5. Optional OAuth Secrets (Google Login)

#### Google OAuth Setup (Optional)
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials

- **Name**: `GOOGLE_CLIENT_ID`
- **Value**: Your Google OAuth client ID
- **Example**: `123456789-abc123.apps.googleusercontent.com`

- **Name**: `GOOGLE_CLIENT_SECRET`
- **Value**: Your Google OAuth client secret
- **Note**: If not provided, Google login will be disabled

## 🔧 VPS SSH Key Setup

### On Your VPS (Hostinger)

1. **Add the public key to authorized_keys**:
```bash
# Copy the PUBLIC key content
cat ~/.ssh/toolcrush_ed25519.pub

# On your VPS, add to authorized_keys
echo "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAI... github-actions@toolcrush" >> ~/.ssh/authorized_keys

# Set proper permissions
chmod 600 ~/.ssh/authorized_keys
chmod 700 ~/.ssh
```

2. **Test SSH connection**:
```bash
ssh -i ~/.ssh/toolcrush_ed25519 root@************
```

### SSH Configuration Best Practices

1. **Disable password authentication** (recommended):
```bash
# Edit SSH config on VPS
sudo nano /etc/ssh/sshd_config

# Set these values:
PasswordAuthentication no
PubkeyAuthentication yes
AuthorizedKeysFile .ssh/authorized_keys

# Restart SSH service
sudo systemctl restart sshd
```

## ✅ Verification Checklist

Before running the deployment pipeline, verify:

- [ ] All 9 required secrets are added to GitHub repository
- [ ] SSH key pair is generated and properly configured
- [ ] Public key is added to VPS authorized_keys
- [ ] SSH connection to VPS works without password
- [ ] MongoDB Atlas connection string is valid
- [ ] Google OAuth credentials are configured with correct redirect URIs
- [ ] Upstash Redis database is created and accessible
- [ ] NextAuth secret is generated and secure (32+ characters)

## 🚨 Security Notes

1. **Never commit secrets to version control**
2. **Use environment-specific secrets** for different deployment stages
3. **Rotate secrets regularly** (every 90 days recommended)
4. **Monitor secret usage** in GitHub Actions logs
5. **Use least-privilege principle** for VPS user permissions
6. **Enable 2FA** on all external services (Google, MongoDB, Upstash)

## 🔍 Troubleshooting

### Common Issues

1. **SSH Permission Denied**:
   - Check private key format and permissions
   - Verify public key is in VPS authorized_keys
   - Ensure SSH service allows key authentication

2. **MongoDB Connection Failed**:
   - Verify connection string format
   - Check network access settings in MongoDB Atlas
   - Ensure database user has proper permissions

3. **Google OAuth Error**:
   - Verify redirect URIs match exactly
   - Check client ID and secret are correct
   - Ensure Google+ API is enabled

4. **Redis Connection Failed**:
   - Verify Upstash database is active
   - Check REST URL and token are correct
   - Ensure database region is accessible

## 🔧 Resolving "Context access might be invalid" Warnings

The GitHub Actions workflow currently shows warnings for the following secrets:
- `VPS_SSH_KEY` (Line 183)
- `VPS_USER` (Line 184)
- `VPS_HOST` (Line 185)
- `MONGODB_URI` (Line 186)
- `NEXTAUTH_SECRET` (Line 187)
- `UPSTASH_REDIS_REST_URL` (Line 188)
- `UPSTASH_REDIS_REST_TOKEN` (Line 189)
- `GOOGLE_CLIENT_ID` (Line 190)
- `GOOGLE_CLIENT_SECRET` (Line 191)

**These warnings will disappear once all secrets are properly configured following this guide.**

### Verification Checklist

After adding all secrets, verify:
- [ ] All 9 secrets appear in repository settings
- [ ] No typos in secret names (case-sensitive)
- [ ] Values are properly formatted (no extra spaces/newlines)
- [ ] SSH key includes complete headers and footers
- [ ] MongoDB URI is properly URL-encoded
- [ ] All external services (MongoDB, Upstash, Google) are active

## 📞 Support

If you encounter issues:
1. Check GitHub Actions logs for specific error messages
2. Verify all secrets are properly formatted
3. Test individual components (SSH, MongoDB, etc.) separately
4. Review this documentation for missed steps
5. Ensure no trailing spaces or newlines in secret values

## 🚀 Next Steps

Once all secrets are configured:
1. Push a commit to `main` branch to trigger deployment
2. Monitor GitHub Actions for successful execution
3. Verify application is accessible at `https://toolrapter.com`
4. Check performance meets requirements (≤20s build, ≤5s page load)

---

**Last Updated**: 2025-07-10
**Version**: 2.0.0 - Added troubleshooting for GitHub Actions warnings
