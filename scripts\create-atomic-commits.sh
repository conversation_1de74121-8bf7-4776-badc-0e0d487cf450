#!/bin/bash

# =============================================================================
# TOOLRAPTER - ATOMIC COMMIT CREATION SCRIPT
# =============================================================================
# Enterprise-grade script to create 75-100 atomic conventional commits
# Domain: toolrapter.com | Repository: ToolCrush
# Follows Conventional Commits specification

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Configuration
TARGET_COMMITS=75
COMMIT_COUNT=0

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to create atomic commit
create_commit() {
    local type="$1"
    local scope="$2"
    local description="$3"
    local files="$4"
    
    # Stage specific files
    if [ -n "$files" ]; then
        git add $files
    else
        git add .
    fi
    
    # Create conventional commit
    local commit_msg="${type}"
    if [ -n "$scope" ]; then
        commit_msg="${commit_msg}(${scope})"
    fi
    commit_msg="${commit_msg}: ${description}"
    
    git commit -m "$commit_msg"
    COMMIT_COUNT=$((COMMIT_COUNT + 1))
    
    log_success "[$COMMIT_COUNT/$TARGET_COMMITS] $commit_msg"
}

# Initialize repository if needed
if [ ! -d ".git" ]; then
    log_info "Initializing Git repository..."
    git init
    git branch -M main
fi

log_info "Creating atomic commits for ToolRapter enterprise deployment..."

# =============================================================================
# INFRASTRUCTURE & CONFIGURATION COMMITS
# =============================================================================

create_commit "feat" "config" "add Next.js 14 configuration with performance optimizations" "next.config.js"
create_commit "feat" "config" "add TypeScript configuration with strict settings" "tsconfig.json"
create_commit "feat" "config" "add Tailwind CSS configuration with custom theme" "tailwind.config.js"
create_commit "feat" "config" "add PostCSS configuration for CSS processing" "postcss.config.js"
create_commit "feat" "config" "add ESLint configuration for code quality" ".eslintrc.json"
create_commit "feat" "config" "add package.json with production dependencies" "package.json"
create_commit "feat" "config" "add environment configuration template" ".env.example"

# =============================================================================
# SECURITY & DEPLOYMENT COMMITS
# =============================================================================

create_commit "security" "headers" "implement enterprise-grade security headers in Next.js" "next.config.js"
create_commit "security" "nginx" "add production Nginx configuration with security headers" "nginx.conf"
create_commit "feat" "deployment" "add PM2 ecosystem configuration for VPS deployment" "ecosystem.config.js"
create_commit "feat" "ci" "add GitHub Actions workflow for VPS deployment" ".github/workflows/deploy-vps.yml"
create_commit "security" "gitignore" "add comprehensive .gitignore for production security" ".gitignore"
create_commit "feat" "scripts" "add VPS deployment automation script" "scripts/deploy-vps.sh"
create_commit "feat" "scripts" "add repository setup automation script" "scripts/setup-repository.sh"

# =============================================================================
# DOCUMENTATION COMMITS
# =============================================================================

create_commit "docs" "readme" "add comprehensive project README with setup instructions" "README.md"
create_commit "docs" "deployment" "add VPS infrastructure setup documentation" "docs/VPS_INFRASTRUCTURE_SETUP.md"
create_commit "docs" "github" "add GitHub repository configuration guide" "docs/GITHUB_REPOSITORY_SETUP.md"
create_commit "docs" "security" "add security implementation documentation" "docs/SECURITY.md"
create_commit "docs" "performance" "add performance optimization guide" "docs/PERFORMANCE.md"
create_commit "docs" "git" "add Git commit strategy documentation" "docs/GIT_COMMIT_STRATEGY.md"
create_commit "docs" "license" "add MIT license for open source compliance" "LICENSE"

# =============================================================================
# CORE APPLICATION STRUCTURE COMMITS
# =============================================================================

create_commit "feat" "app" "initialize Next.js 14 app directory structure" "src/app/"
create_commit "feat" "layout" "add root layout with metadata and providers" "src/app/layout.tsx"
create_commit "feat" "page" "add home page with hero section" "src/app/page.tsx"
create_commit "feat" "globals" "add global CSS styles with Tailwind imports" "src/styles.css"
create_commit "feat" "components" "add UI components directory structure" "src/components/"
create_commit "feat" "lib" "add utility libraries and configurations" "src/lib/"
create_commit "feat" "types" "add TypeScript type definitions" "src/types/"

# =============================================================================
# UI COMPONENTS COMMITS
# =============================================================================

create_commit "feat" "ui" "add Button component with variants and animations" "src/components/ui/button.tsx"
create_commit "feat" "ui" "add Card component with hover effects" "src/components/ui/card.tsx"
create_commit "feat" "ui" "add Input component with validation states" "src/components/ui/input.tsx"
create_commit "feat" "ui" "add Modal component with accessibility features" "src/components/ui/modal.tsx"
create_commit "feat" "ui" "add Navigation component with responsive design" "src/components/ui/navigation.tsx"
create_commit "feat" "ui" "add Footer component with social links" "src/components/ui/footer.tsx"
create_commit "feat" "ui" "add Loading component with skeleton states" "src/components/ui/loading.tsx"
create_commit "feat" "ui" "add Toast notification component" "src/components/ui/toast.tsx"

# =============================================================================
# LAYOUT & NAVIGATION COMMITS
# =============================================================================

create_commit "feat" "layout" "add responsive header with navigation menu" "src/components/layout/header.tsx"
create_commit "feat" "layout" "add sidebar navigation for admin panel" "src/components/layout/sidebar.tsx"
create_commit "feat" "layout" "add breadcrumb navigation component" "src/components/layout/breadcrumb.tsx"
create_commit "feat" "layout" "add mobile navigation drawer" "src/components/layout/mobile-nav.tsx"
create_commit "feat" "layout" "add search functionality in header" "src/components/layout/search.tsx"

# =============================================================================
# AUTHENTICATION & SECURITY COMMITS
# =============================================================================

create_commit "feat" "auth" "add NextAuth.js configuration with providers" "src/app/api/auth/[...nextauth]/route.ts"
create_commit "feat" "auth" "add login page with form validation" "src/app/auth/login/page.tsx"
create_commit "feat" "auth" "add registration page with security checks" "src/app/auth/register/page.tsx"
create_commit "feat" "auth" "add password reset functionality" "src/app/auth/reset-password/page.tsx"
create_commit "security" "middleware" "add authentication middleware with rate limiting" "src/middleware.ts"
create_commit "security" "auth" "add role-based access control system" "src/lib/auth.ts"
create_commit "security" "validation" "add input validation with Zod schemas" "src/lib/validations.ts"

# =============================================================================
# DATABASE & API COMMITS
# =============================================================================

create_commit "feat" "database" "add MongoDB connection configuration" "src/lib/mongodb.ts"
create_commit "feat" "models" "add User model with authentication fields" "src/models/User.ts"
create_commit "feat" "models" "add Tool model for dynamic tool management" "src/models/Tool.ts"
create_commit "feat" "models" "add Blog model for content management" "src/models/Blog.ts"
create_commit "feat" "models" "add Contact model for form submissions" "src/models/Contact.ts"
create_commit "feat" "api" "add user management API endpoints" "src/app/api/users/"
create_commit "feat" "api" "add tool management API endpoints" "src/app/api/tools/"
create_commit "feat" "api" "add blog management API endpoints" "src/app/api/blog/"
create_commit "feat" "api" "add contact form API endpoint" "src/app/api/contact/route.ts"
create_commit "feat" "api" "add health check endpoint for monitoring" "src/app/api/health/route.ts"

# =============================================================================
# ADMIN PANEL COMMITS
# =============================================================================

create_commit "feat" "admin" "add admin dashboard layout" "src/app/admin/layout.tsx"
create_commit "feat" "admin" "add admin dashboard overview page" "src/app/admin/page.tsx"
create_commit "feat" "admin" "add user management interface" "src/app/admin/users/page.tsx"
create_commit "feat" "admin" "add tool management interface" "src/app/admin/tools/page.tsx"
create_commit "feat" "admin" "add blog management interface" "src/app/admin/blog/page.tsx"
create_commit "feat" "admin" "add contact management interface" "src/app/admin/contacts/page.tsx"
create_commit "feat" "admin" "add analytics dashboard" "src/app/admin/analytics/page.tsx"
create_commit "feat" "admin" "add settings management page" "src/app/admin/settings/page.tsx"

# =============================================================================
# TOOLS & CALCULATORS COMMITS
# =============================================================================

create_commit "feat" "tools" "add tools listing page with search and filters" "src/app/tools/page.tsx"
create_commit "feat" "tools" "add dynamic tool page component" "src/app/tools/[slug]/page.tsx"
create_commit "feat" "calculators" "add percentage calculator component" "src/components/calculators/percentage.tsx"
create_commit "feat" "calculators" "add age calculator component" "src/components/calculators/age.tsx"
create_commit "feat" "calculators" "add BMI calculator component" "src/components/calculators/bmi.tsx"
create_commit "feat" "calculators" "add loan calculator component" "src/components/calculators/loan.tsx"
create_commit "feat" "calculators" "add currency converter component" "src/components/calculators/currency.tsx"

# =============================================================================
# BLOG SYSTEM COMMITS
# =============================================================================

create_commit "feat" "blog" "add blog listing page with pagination" "src/app/blog/page.tsx"
create_commit "feat" "blog" "add dynamic blog post page" "src/app/blog/[slug]/page.tsx"
create_commit "feat" "blog" "add blog category pages" "src/app/blog/category/[category]/page.tsx"
create_commit "feat" "blog" "add blog search functionality" "src/app/blog/search/page.tsx"
create_commit "feat" "blog" "add rich text editor for blog posts" "src/components/blog/editor.tsx"
create_commit "feat" "blog" "add blog post card component" "src/components/blog/post-card.tsx"
create_commit "feat" "blog" "add blog sidebar with categories" "src/components/blog/sidebar.tsx"

# =============================================================================
# PERFORMANCE & OPTIMIZATION COMMITS
# =============================================================================

create_commit "perf" "images" "optimize image loading with Next.js Image component" "src/components/ui/optimized-image.tsx"
create_commit "perf" "fonts" "add font optimization script" "scripts/download-fonts.js"
create_commit "perf" "seo" "add SEO optimization utilities" "src/lib/seo.ts"
create_commit "perf" "sitemap" "add dynamic sitemap generation" "src/app/sitemap.ts"
create_commit "perf" "robots" "add robots.txt generation" "src/app/robots.ts"
create_commit "perf" "cache" "implement Redis caching for API responses" "src/lib/cache.ts"

# =============================================================================
# TESTING & QUALITY COMMITS
# =============================================================================

create_commit "test" "setup" "add Jest testing configuration" "jest.config.js jest.setup.js"
create_commit "test" "components" "add unit tests for UI components" "src/components/__tests__/"
create_commit "test" "api" "add API endpoint tests" "src/app/api/__tests__/"
create_commit "test" "utils" "add utility function tests" "src/lib/__tests__/"

log_success "Created $COMMIT_COUNT atomic commits following enterprise standards!"
log_info "Repository is ready for production deployment to toolrapter.com"
