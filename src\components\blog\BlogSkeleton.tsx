'use client';

import React from "react";
import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";

interface BlogSkeletonProps {
  count?: number;
  layout?: 'grid' | 'masonry';
  className?: string;
}

const skeletonVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: {
      duration: 0.4,
      ease: "easeOut"
    }
  }
};

const shimmerVariants = {
  initial: { x: '-100%' },
  animate: { 
    x: '100%',
    transition: {
      duration: 1.5,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
};

function SkeletonCard({ index }: { index: number }) {
  return (
    <motion.div
      variants={skeletonVariants}
      initial="hidden"
      animate="visible"
      transition={{ delay: index * 0.1 }}
      className="w-full"
    >
      <Card className="overflow-hidden border-border/50">
        <CardContent className="p-0">
          {/* Image Skeleton */}
          <div className="relative h-52 bg-muted overflow-hidden">
            <motion.div
              variants={shimmerVariants}
              initial="initial"
              animate="animate"
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent"
            />
            {/* Category Badge Skeleton */}
            <div className="absolute top-4 left-4">
              <div className="h-6 w-20 bg-muted-foreground/20 rounded-full" />
            </div>
          </div>

          {/* Content Skeleton */}
          <div className="p-6 space-y-4">
            {/* Date Skeleton */}
            <div className="flex items-center space-x-2">
              <div className="h-4 w-4 bg-muted-foreground/20 rounded" />
              <div className="h-4 w-24 bg-muted-foreground/20 rounded" />
            </div>

            {/* Title Skeleton */}
            <div className="space-y-2">
              <div className="h-6 w-full bg-muted-foreground/20 rounded" />
              <div className="h-6 w-3/4 bg-muted-foreground/20 rounded" />
            </div>

            {/* Description Skeleton */}
            <div className="space-y-2">
              <div className="h-4 w-full bg-muted-foreground/20 rounded" />
              <div className="h-4 w-5/6 bg-muted-foreground/20 rounded" />
              <div className="h-4 w-2/3 bg-muted-foreground/20 rounded" />
            </div>

            {/* Author Skeleton */}
            <div className="flex items-center space-x-2">
              <div className="h-4 w-4 bg-muted-foreground/20 rounded" />
              <div className="h-4 w-20 bg-muted-foreground/20 rounded" />
            </div>

            {/* Read More Skeleton */}
            <div className="flex items-center space-x-2">
              <div className="h-4 w-16 bg-muted-foreground/20 rounded" />
              <div className="h-4 w-4 bg-muted-foreground/20 rounded" />
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

export function BlogSkeleton({ 
  count = 9, 
  layout = 'masonry',
  className = "" 
}: BlogSkeletonProps) {
  const skeletonItems = Array.from({ length: count }, (_, index) => (
    <SkeletonCard key={index} index={index} />
  ));

  if (layout === 'masonry') {
    return (
      <div className={`w-full ${className}`}>
        <div
          className="pinterest-skeleton-masonry"
          style={{
            columns: 'auto',
            columnWidth: '350px',
            columnGap: '2rem',
            columnFill: 'balance',
            overflow: 'visible',
            height: 'auto',
          }}
        >
          {skeletonItems.map((item, index) => (
            <div
              key={index}
              className="break-inside-avoid inline-block w-full"
              style={{ marginBottom: '1.5rem', overflow: 'visible' }}
            >
              {item}
            </div>
          ))}
        </div>

        {/* Responsive CSS for skeleton masonry */}
        <style>{`
          .pinterest-skeleton-masonry {
            overflow: visible !important;
            height: auto !important;
          }
          
          @media (max-width: 768px) {
            .pinterest-skeleton-masonry {
              columns: 1 !important;
              column-gap: 1rem !important;
            }
          }

          @media (min-width: 769px) and (max-width: 1024px) {
            .pinterest-skeleton-masonry {
              columns: 2 !important;
              column-gap: 1.5rem !important;
            }
          }

          @media (min-width: 1025px) and (max-width: 1280px) {
            .pinterest-skeleton-masonry {
              columns: 3 !important;
              column-gap: 2rem !important;
            }
          }

          @media (min-width: 1281px) {
            .pinterest-skeleton-masonry {
              columns: 4 !important;
              column-gap: 2rem !important;
            }
          }
        `}</style>
      </div>
    );
  }

  // Grid layout for skeleton
  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 ${className}`}>
      {skeletonItems}
    </div>
  );
}

// Compact skeleton for admin panels
export function CompactBlogSkeleton({ count = 6 }: { count?: number }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {Array.from({ length: count }).map((_, index) => (
        <motion.div
          key={index}
          variants={skeletonVariants}
          initial="hidden"
          animate="visible"
          transition={{ delay: index * 0.05 }}
        >
          <Card className="overflow-hidden border-border/50">
            <CardContent className="p-0">
              {/* Compact Image Skeleton */}
              <div className="relative h-32 bg-muted overflow-hidden">
                <motion.div
                  variants={shimmerVariants}
                  initial="initial"
                  animate="animate"
                  className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent"
                />
              </div>

              {/* Compact Content Skeleton */}
              <div className="p-4 space-y-3">
                <div className="h-5 w-full bg-muted-foreground/20 rounded" />
                <div className="h-4 w-3/4 bg-muted-foreground/20 rounded" />
                <div className="flex justify-between items-center">
                  <div className="h-3 w-16 bg-muted-foreground/20 rounded" />
                  <div className="h-6 w-20 bg-muted-foreground/20 rounded" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </div>
  );
}
