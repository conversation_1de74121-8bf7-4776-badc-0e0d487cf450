# =============================================================================
# TOOLCRUSH - PRODUCTION-READY NEXT.JS 14 GITIGNORE
# =============================================================================
# Enterprise-grade .gitignore for Next.js 14 with TypeScript, security, and deployment

# =============================================================================
# DEPENDENCIES & PACKAGE MANAGERS
# =============================================================================
node_modules/
.pnp
.pnp.js
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# =============================================================================
# NEXT.JS BUILD & CACHE
# =============================================================================
.next/
out/
build/
dist/
.swc/

# Next.js cache
.next/cache/
.next/static/chunks/
.next/server/

# =============================================================================
# ENVIRONMENT & CONFIGURATION
# =============================================================================
# Environment files (NEVER commit these)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# Configuration files with sensitive data
.env.example.filled
config.local.js
secrets.json

# =============================================================================
# TESTING & COVERAGE
# =============================================================================
coverage/
.nyc_output/
.coverage/
*.lcov
junit.xml
test-results/
playwright-report/
test-results.xml

# Jest
jest-coverage/
__tests__/__snapshots__/

# =============================================================================
# TYPESCRIPT & BUILD ARTIFACTS
# =============================================================================
*.tsbuildinfo
next-env.d.ts
*.d.ts.map
.tsbuildinfo

# TypeScript incremental compilation
tsconfig.tsbuildinfo

# =============================================================================
# LOGS & DEBUG
# =============================================================================
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# =============================================================================
# OPERATING SYSTEM FILES
# =============================================================================
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# IDE & EDITOR FILES
# =============================================================================
# VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# JetBrains IDEs
.idea/
*.swp
*.swo
*~

# Sublime Text
*.tmlanguage.cache
*.tmPreferences.cache
*.stTheme.cache
*.sublime-workspace
*.sublime-project

# Vim
[._]*.s[a-v][a-z]
[._]*.sw[a-p]
[._]s[a-rt-v][a-z]
[._]ss[a-gi-z]
[._]sw[a-p]

# =============================================================================
# DEPLOYMENT & HOSTING
# =============================================================================
# VPS Deployment
deployment.tar.gz
deployment/
.deployment/

# PM2 Process Manager
.pm2/
pm2.log
ecosystem.config.local.js

# Nginx Configuration
nginx.local.conf
ssl/

# Netlify
.netlify/

# AWS
.aws/

# Docker
.dockerignore
Dockerfile.local
docker-compose.override.yml

# SSH Keys & Certificates
*.pem
*.key
*.crt
*.csr
id_rsa*
authorized_keys

# =============================================================================
# SECURITY & CERTIFICATES
# =============================================================================
# SSL certificates
*.pem
*.key
*.crt
*.csr
*.p12
*.pfx

# Security files
.htpasswd
.htaccess
auth.json
credentials.json

# =============================================================================
# MEDIA & UPLOADS
# =============================================================================
# User uploads (if stored locally)
public/uploads/
uploads/
temp/
tmp/

# Generated images
public/generated/
.cache/

# =============================================================================
# DATABASE & STORAGE
# =============================================================================
# Local database files
*.db
*.sqlite
*.sqlite3
data/
db/

# MongoDB
dump/

# Redis
dump.rdb

# =============================================================================
# MONITORING & ANALYTICS
# =============================================================================
# Sentry
.sentryclirc

# Performance monitoring
.lighthouseci/

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================
# Storybook
storybook-static/
.storybook/public/

# ESLint cache
.eslintcache

# Prettier cache
.prettiercache

# Stylelint cache
.stylelintcache

# =============================================================================
# TEMPORARY & CACHE FILES
# =============================================================================
# Temporary folders
.tmp/
.temp/
.cache/

# Runtime cache
.runtime/

# =============================================================================
# DOCUMENTATION GENERATION
# =============================================================================
# Generated docs
docs/build/
.docusaurus/

# =============================================================================
# MOBILE & NATIVE
# =============================================================================
# React Native
.expo/
.expo-shared/

# =============================================================================
# PACKAGE MANAGER LOCKS (OPTIONAL)
# =============================================================================
# Uncomment if you want to ignore lock files (not recommended for production)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# =============================================================================
# PROJECT-SPECIFIC EXCLUSIONS
# =============================================================================
# Cleanup documentation (temporary)
CLEANUP_RECOMMENDATIONS.md
DELETION_REPORT.md
cleanup-script.ps1
enhanced-cleanup-script.ps1

# Audit reports (temporary)
AUDIT_FIXES_SUMMARY.md
COMPREHENSIVE_PROJECT_AUDIT_REPORT.md
PERFORMANCE_FIXES_SUMMARY.md

# Development documentation (keep in repo but ignore if needed)
# AUTH_SETUP.md
# CONTACT_ADMIN_SETUP.md
# SECURITY_ENVIRONMENT_SETUP.md

# =============================================================================
# NOTES
# =============================================================================
# This .gitignore is designed for:
# - Next.js 14 with App Router
# - TypeScript
# - Enterprise security requirements
# - Multiple deployment targets
# - Team collaboration
# - CI/CD pipelines
#
# Security reminder: NEVER commit:
# - Environment files with real credentials
# - SSL certificates or private keys
# - Database files with real data
# - API keys or secrets
# - User uploads with sensitive content
\[A\[A\[A\[A\[C\[B\[B\[C\[A\[B\[C\[C\[C\[C\[C\[C\[C\[C\[C\[C\[A\[C\[C\[C\[C\[C\[C\[C\[C\[C\[C\[C\[C\[C\[C\[C\[<EMAIL>\[C\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[.pub
\[A\[A\[A\[A\[C\[B\[B\[C\[A\[B\[C\[C\[C\[C\[C\[C\[C\[C\[C\[C\[A\[C\[C\[C\[C\[C\[C\[C\[C\[C\[C\[C\[C\[C\[C\[C\[<EMAIL>\[C\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[D\[
